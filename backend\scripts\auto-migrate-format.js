const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

function log(color, message) {
  const colors = {
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m',
    reset: '\x1b[0m'
  };
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// Generate new ID with unified format: {PREFIX}{YYYYMMDD}{SEQUENCE}
function generateUnifiedId(prefix, date = new Date(), sequence = 1) {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const seq = String(sequence).padStart(3, '0');
  
  return `${prefix}${year}${month}${day}${seq}`;
}

async function createBackup() {
  log('cyan', '📦 Creating backup...');
  
  try {
    const tables = ['doctors', 'patients', 'appointments', 'departments', 'rooms', 'medical_records'];
    const backup = {
      timestamp: new Date().toISOString(),
      tables: {}
    };
    
    for (const table of tables) {
      const { data, error } = await supabase.from(table).select('*');
      if (error) {
        log('yellow', `⚠️  Could not backup ${table}: ${error.message}`);
        backup.tables[table] = [];
      } else {
        backup.tables[table] = data || [];
        log('blue', `   ✅ ${table}: ${data?.length || 0} records`);
      }
    }
    
    const fs = require('fs');
    const backupFile = `backup-${Date.now()}.json`;
    fs.writeFileSync(backupFile, JSON.stringify(backup, null, 2));
    
    log('green', `✅ Backup created: ${backupFile}`);
    return backupFile;
  } catch (error) {
    log('red', `❌ Backup failed: ${error.message}`);
    throw error;
  }
}

async function migrateDoctors() {
  log('cyan', '\n🔧 Migrating Doctors...');
  
  try {
    const { data: doctors } = await supabase.from('doctors').select('*');
    const idMapping = {};
    
    for (let i = 0; i < doctors.length; i++) {
      const doctor = doctors[i];
      const oldId = doctor.doctor_id;
      const newId = generateUnifiedId('DOC', new Date(), i + 1);
      
      idMapping[oldId] = newId;
      
      log('yellow', `   ${oldId} → ${newId}`);
      
      const { error } = await supabase
        .from('doctors')
        .update({ doctor_id: newId })
        .eq('doctor_id', oldId);
        
      if (error) {
        log('red', `❌ Failed to update ${oldId}: ${error.message}`);
      } else {
        log('green', `✅ Updated ${oldId}`);
      }
    }
    
    return idMapping;
  } catch (error) {
    log('red', `❌ Doctor migration failed: ${error.message}`);
    throw error;
  }
}

async function migratePatients() {
  log('cyan', '\n🔧 Migrating Patients...');
  
  try {
    const { data: patients } = await supabase.from('patients').select('*');
    const idMapping = {};
    
    for (let i = 0; i < patients.length; i++) {
      const patient = patients[i];
      const oldId = patient.patient_id;
      const newId = generateUnifiedId('PAT', new Date(), i + 1);
      
      idMapping[oldId] = newId;
      
      log('yellow', `   ${oldId} → ${newId}`);
      
      const { error } = await supabase
        .from('patients')
        .update({ patient_id: newId })
        .eq('patient_id', oldId);
        
      if (error) {
        log('red', `❌ Failed to update ${oldId}: ${error.message}`);
      } else {
        log('green', `✅ Updated ${oldId}`);
      }
    }
    
    return idMapping;
  } catch (error) {
    log('red', `❌ Patient migration failed: ${error.message}`);
    throw error;
  }
}

async function migrateDepartments() {
  log('cyan', '\n🔧 Migrating Departments...');
  
  try {
    const { data: departments } = await supabase.from('departments').select('*');
    const idMapping = {};
    
    for (let i = 0; i < departments.length; i++) {
      const department = departments[i];
      const oldId = department.department_id;
      const newId = generateUnifiedId('DEPT', new Date(), i + 1);
      
      idMapping[oldId] = newId;
      
      log('yellow', `   ${oldId} → ${newId}`);
      
      const { error } = await supabase
        .from('departments')
        .update({ department_id: newId })
        .eq('department_id', oldId);
        
      if (error) {
        log('red', `❌ Failed to update ${oldId}: ${error.message}`);
      } else {
        log('green', `✅ Updated ${oldId}`);
      }
    }
    
    return idMapping;
  } catch (error) {
    log('red', `❌ Department migration failed: ${error.message}`);
    throw error;
  }
}

async function updateAppointments(doctorMapping, patientMapping) {
  log('cyan', '\n🔧 Updating Appointments...');
  
  try {
    const { data: appointments } = await supabase.from('appointments').select('*');
    
    for (let i = 0; i < appointments.length; i++) {
      const appointment = appointments[i];
      const oldId = appointment.appointment_id;
      const newId = generateUnifiedId('APT', new Date(), i + 1);
      
      const updates = { appointment_id: newId };
      
      if (doctorMapping[appointment.doctor_id]) {
        updates.doctor_id = doctorMapping[appointment.doctor_id];
      }
      
      if (patientMapping[appointment.patient_id]) {
        updates.patient_id = patientMapping[appointment.patient_id];
      }
      
      log('yellow', `   ${oldId} → ${newId}`);
      
      const { error } = await supabase
        .from('appointments')
        .update(updates)
        .eq('appointment_id', oldId);
        
      if (error) {
        log('red', `❌ Failed to update ${oldId}: ${error.message}`);
      } else {
        log('green', `✅ Updated ${oldId}`);
      }
    }
  } catch (error) {
    log('red', `❌ Appointment update failed: ${error.message}`);
    throw error;
  }
}

async function updateRooms(departmentMapping) {
  log('cyan', '\n🔧 Updating Rooms...');
  
  try {
    const { data: rooms } = await supabase.from('rooms').select('*');
    
    for (let i = 0; i < rooms.length; i++) {
      const room = rooms[i];
      const oldId = room.room_id;
      const newId = generateUnifiedId('ROOM', new Date(), i + 1);
      
      const updates = { room_id: newId };
      
      if (departmentMapping[room.department_id]) {
        updates.department_id = departmentMapping[room.department_id];
      }
      
      log('yellow', `   ${oldId} → ${newId}`);
      
      const { error } = await supabase
        .from('rooms')
        .update(updates)
        .eq('room_id', oldId);
        
      if (error) {
        log('red', `❌ Failed to update ${oldId}: ${error.message}`);
      } else {
        log('green', `✅ Updated ${oldId}`);
      }
    }
  } catch (error) {
    log('red', `❌ Room update failed: ${error.message}`);
    throw error;
  }
}

async function updateMedicalRecords(doctorMapping, patientMapping) {
  log('cyan', '\n🔧 Updating Medical Records...');
  
  try {
    const { data: records } = await supabase.from('medical_records').select('*');
    
    for (let i = 0; i < records.length; i++) {
      const record = records[i];
      const oldId = record.record_id;
      const newId = generateUnifiedId('MED', new Date(), i + 1);
      
      const updates = { record_id: newId };
      
      if (doctorMapping[record.doctor_id]) {
        updates.doctor_id = doctorMapping[record.doctor_id];
      }
      
      if (patientMapping[record.patient_id]) {
        updates.patient_id = patientMapping[record.patient_id];
      }
      
      log('yellow', `   ${oldId} → ${newId}`);
      
      const { error } = await supabase
        .from('medical_records')
        .update(updates)
        .eq('record_id', oldId);
        
      if (error) {
        log('red', `❌ Failed to update ${oldId}: ${error.message}`);
      } else {
        log('green', `✅ Updated ${oldId}`);
      }
    }
  } catch (error) {
    log('red', `❌ Medical record update failed: ${error.message}`);
    throw error;
  }
}

async function verifyMigration() {
  log('cyan', '\n🔍 Verifying migration...');
  
  const checks = [
    { table: 'doctors', pattern: /^DOC\d{8}\d{3}$/, field: 'doctor_id' },
    { table: 'patients', pattern: /^PAT\d{8}\d{3}$/, field: 'patient_id' },
    { table: 'appointments', pattern: /^APT\d{8}\d{3}$/, field: 'appointment_id' },
    { table: 'departments', pattern: /^DEPT\d{8}\d{3}$/, field: 'department_id' },
    { table: 'rooms', pattern: /^ROOM\d{8}\d{3}$/, field: 'room_id' },
    { table: 'medical_records', pattern: /^MED\d{8}\d{3}$/, field: 'record_id' }
  ];
  
  let allPassed = true;
  
  for (const check of checks) {
    try {
      const { data } = await supabase.from(check.table).select('*');
      const invalidIds = data?.filter(record => !check.pattern.test(record[check.field])) || [];
      
      if (invalidIds.length === 0) {
        log('green', `   ✅ ${check.table}: All ${data?.length || 0} records valid`);
      } else {
        log('red', `   ❌ ${check.table}: ${invalidIds.length} invalid records`);
        allPassed = false;
      }
    } catch (error) {
      log('red', `   ❌ ${check.table}: Verification failed`);
      allPassed = false;
    }
  }
  
  return allPassed;
}

async function main() {
  log('cyan', '🚀 Hospital Management - Auto Format Migration');
  log('cyan', '===============================================');
  
  try {
    // Step 1: Backup
    const backupFile = await createBackup();
    
    // Step 2: Migrate core entities
    const doctorMapping = await migrateDoctors();
    const patientMapping = await migratePatients();
    const departmentMapping = await migrateDepartments();
    
    // Step 3: Update dependent entities
    await updateAppointments(doctorMapping, patientMapping);
    await updateRooms(departmentMapping);
    await updateMedicalRecords(doctorMapping, patientMapping);
    
    // Step 4: Verify
    const success = await verifyMigration();
    
    if (success) {
      log('green', '\n🎉 Migration completed successfully!');
      log('blue', `📦 Backup: ${backupFile}`);
      log('cyan', '\n✅ New format standards applied:');
      log('yellow', '   - DOC{YYYYMMDD}{XXX} for doctors');
      log('yellow', '   - PAT{YYYYMMDD}{XXX} for patients');
      log('yellow', '   - APT{YYYYMMDD}{XXX} for appointments');
      log('yellow', '   - DEPT{YYYYMMDD}{XXX} for departments');
      log('yellow', '   - ROOM{YYYYMMDD}{XXX} for rooms');
      log('yellow', '   - MED{YYYYMMDD}{XXX} for medical records');
    } else {
      log('red', '\n❌ Migration completed with issues');
    }
    
  } catch (error) {
    log('red', `❌ Migration failed: ${error.message}`);
    process.exit(1);
  }
}

main();
