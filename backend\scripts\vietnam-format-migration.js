const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

function log(color, message) {
  const colors = {
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m',
    reset: '\x1b[0m'
  };
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// Generate Vietnam-focused ID format: {PREFIX}{6DIGITS}
function generateVietnamId(prefix, sequence = 1) {
  const seq = String(sequence).padStart(6, '0');
  return `${prefix}${seq}`;
}

// Fix phone number to Vietnam format (10 digits starting with 0)
function fixPhoneNumber(phone) {
  if (!phone) return null;
  
  // Remove all non-digits
  const digits = phone.replace(/\D/g, '');
  
  // Handle different cases
  if (digits.length === 10 && digits.startsWith('0')) {
    return digits; // Already correct
  } else if (digits.length === 11 && digits.startsWith('84')) {
    return '0' + digits.substring(2); // Convert from +84 format
  } else if (digits.length === 9) {
    return '0' + digits; // Add leading 0
  } else if (digits.length === 10 && !digits.startsWith('0')) {
    return '0' + digits.substring(1); // Replace first digit with 0
  }
  
  // If can't fix, return original
  return phone;
}

// Fix license number to Vietnam format: VN-XX-1234
function fixLicenseNumber(license) {
  if (!license) return 'VN-BS-0001';
  
  // Extract letters and numbers
  const letters = license.match(/[A-Z]+/g)?.join('') || 'BS';
  const numbers = license.match(/\d+/g)?.join('') || '0001';
  
  // Take first 2-4 letters and first 4 digits
  const prefix = letters.substring(0, 4);
  const suffix = numbers.padStart(4, '0').substring(0, 4);
  
  return `VN-${prefix}-${suffix}`;
}

async function createBackup() {
  log('cyan', '📦 Creating backup for Vietnam migration...');
  
  try {
    const tables = ['doctors', 'patients', 'appointments', 'departments', 'rooms', 'medical_records'];
    const backup = {
      timestamp: new Date().toISOString(),
      migration_type: 'vietnam_focused',
      tables: {}
    };
    
    for (const table of tables) {
      const { data, error } = await supabase.from(table).select('*');
      if (error) {
        log('yellow', `⚠️  Could not backup ${table}: ${error.message}`);
        backup.tables[table] = [];
      } else {
        backup.tables[table] = data || [];
        log('blue', `   ✅ ${table}: ${data?.length || 0} records`);
      }
    }
    
    const fs = require('fs');
    const backupFile = `vietnam-backup-${Date.now()}.json`;
    fs.writeFileSync(backupFile, JSON.stringify(backup, null, 2));
    
    log('green', `✅ Backup created: ${backupFile}`);
    return backupFile;
  } catch (error) {
    log('red', `❌ Backup failed: ${error.message}`);
    throw error;
  }
}

async function vietnamMigration() {
  log('cyan', '\n🇻🇳 Starting Vietnam-focused migration...');
  
  try {
    // Get all data
    const { data: doctors } = await supabase.from('doctors').select('*');
    const { data: patients } = await supabase.from('patients').select('*');
    const { data: departments } = await supabase.from('departments').select('*');
    const { data: rooms } = await supabase.from('rooms').select('*');
    const { data: appointments } = await supabase.from('appointments').select('*');
    const { data: medicalRecords } = await supabase.from('medical_records').select('*');
    
    // Create ID mappings with Vietnam format
    const doctorMapping = {};
    const patientMapping = {};
    const departmentMapping = {};
    const roomMapping = {};
    const appointmentMapping = {};
    const medicalRecordMapping = {};
    
    // Generate new Vietnam-style IDs
    doctors.forEach((doctor, i) => {
      doctorMapping[doctor.doctor_id] = generateVietnamId('DOC', i + 1);
    });
    
    patients.forEach((patient, i) => {
      patientMapping[patient.patient_id] = generateVietnamId('BN', i + 1);
    });
    
    departments.forEach((dept, i) => {
      departmentMapping[dept.department_id] = generateVietnamId('KHOA', i + 1);
    });
    
    rooms.forEach((room, i) => {
      roomMapping[room.room_id] = generateVietnamId('PHONG', i + 1);
    });
    
    appointments.forEach((apt, i) => {
      appointmentMapping[apt.appointment_id] = generateVietnamId('LK', i + 1);
    });
    
    medicalRecords.forEach((record, i) => {
      medicalRecordMapping[record.record_id] = generateVietnamId('HSBA', i + 1);
    });
    
    log('blue', '📋 Vietnam ID Mappings:');
    log('yellow', `   Doctors: DOC000001-DOC${String(doctors.length).padStart(6, '0')}`);
    log('yellow', `   Patients: BN000001-BN${String(patients.length).padStart(6, '0')}`);
    log('yellow', `   Departments: KHOA000001-KHOA${String(departments.length).padStart(6, '0')}`);
    log('yellow', `   Rooms: PHONG000001-PHONG${String(rooms.length).padStart(6, '0')}`);
    log('yellow', `   Appointments: LK000001-LK${String(appointments.length).padStart(6, '0')}`);
    log('yellow', `   Medical Records: HSBA000001-HSBA${String(medicalRecords.length).padStart(6, '0')}`);
    
    // Clear dependent tables first
    log('cyan', '\n🗑️  Clearing dependent records...');
    await supabase.from('medical_records').delete().neq('record_id', 'dummy');
    await supabase.from('appointments').delete().neq('appointment_id', 'dummy');
    await supabase.from('rooms').delete().neq('room_id', 'dummy');
    log('yellow', '   ✅ Cleared dependent records');
    
    // Update core entities with Vietnam format
    log('cyan', '\n🔄 Updating to Vietnam format...');
    
    // Update departments
    for (const dept of departments) {
      const newId = departmentMapping[dept.department_id];
      
      await supabase.from('departments').delete().eq('department_id', dept.department_id);
      
      const { error } = await supabase.from('departments').insert({
        ...dept,
        department_id: newId
      });
      
      if (error) {
        log('red', `❌ Failed to update department ${dept.department_id}: ${error.message}`);
      } else {
        log('green', `   ✅ ${dept.department_id} → ${newId}`);
      }
    }
    
    // Update doctors with Vietnam format
    for (const doctor of doctors) {
      const newId = doctorMapping[doctor.doctor_id];
      const newDeptId = departmentMapping[doctor.department_id] || doctor.department_id;
      const fixedLicense = fixLicenseNumber(doctor.license_number);
      
      await supabase.from('doctors').delete().eq('doctor_id', doctor.doctor_id);
      
      const { error } = await supabase.from('doctors').insert({
        ...doctor,
        doctor_id: newId,
        department_id: newDeptId,
        license_number: fixedLicense
      });
      
      if (error) {
        log('red', `❌ Failed to update doctor ${doctor.doctor_id}: ${error.message}`);
      } else {
        log('green', `   ✅ ${doctor.doctor_id} → ${newId}`);
        log('blue', `      License: ${doctor.license_number} → ${fixedLicense}`);
      }
    }
    
    // Update patients with Vietnam format
    for (const patient of patients) {
      const newId = patientMapping[patient.patient_id];
      
      await supabase.from('patients').delete().eq('patient_id', patient.patient_id);
      
      const { error } = await supabase.from('patients').insert({
        ...patient,
        patient_id: newId
      });
      
      if (error) {
        log('red', `❌ Failed to update patient ${patient.patient_id}: ${error.message}`);
      } else {
        log('green', `   ✅ ${patient.patient_id} → ${newId}`);
      }
    }
    
    // Re-insert dependent records with new IDs
    log('cyan', '\n🔄 Re-inserting dependent records...');
    
    // Re-insert rooms
    for (const room of rooms) {
      const newRoomData = {
        ...room,
        room_id: roomMapping[room.room_id],
        department_id: departmentMapping[room.department_id] || room.department_id
      };
      
      const { error } = await supabase.from('rooms').insert(newRoomData);
      
      if (error) {
        log('red', `❌ Failed to insert room: ${error.message}`);
      } else {
        log('green', `   ✅ ${room.room_id} → ${newRoomData.room_id}`);
      }
    }
    
    // Re-insert appointments
    for (const appointment of appointments) {
      const newAppointmentData = {
        ...appointment,
        appointment_id: appointmentMapping[appointment.appointment_id],
        doctor_id: doctorMapping[appointment.doctor_id] || appointment.doctor_id,
        patient_id: patientMapping[appointment.patient_id] || appointment.patient_id,
        room_id: roomMapping[appointment.room_id] || appointment.room_id
      };
      
      const { error } = await supabase.from('appointments').insert(newAppointmentData);
      
      if (error) {
        log('red', `❌ Failed to insert appointment: ${error.message}`);
      } else {
        log('green', `   ✅ ${appointment.appointment_id} → ${newAppointmentData.appointment_id}`);
      }
    }
    
    // Re-insert medical records
    for (const record of medicalRecords) {
      const newRecordData = {
        ...record,
        record_id: medicalRecordMapping[record.record_id],
        doctor_id: doctorMapping[record.doctor_id] || record.doctor_id,
        patient_id: patientMapping[record.patient_id] || record.patient_id,
        appointment_id: appointmentMapping[record.appointment_id] || record.appointment_id
      };
      
      const { error } = await supabase.from('medical_records').insert(newRecordData);
      
      if (error) {
        log('red', `❌ Failed to insert medical record: ${error.message}`);
      } else {
        log('green', `   ✅ ${record.record_id} → ${newRecordData.record_id}`);
      }
    }
    
    return true;
    
  } catch (error) {
    log('red', `❌ Vietnam migration failed: ${error.message}`);
    throw error;
  }
}

async function verifyVietnamFormat() {
  log('cyan', '\n🔍 Verifying Vietnam format compliance...');
  
  const vietnamPatterns = {
    doctors: /^DOC\d{6}$/,
    patients: /^BN\d{6}$/,
    appointments: /^LK\d{6}$/,
    departments: /^KHOA\d{6}$/,
    rooms: /^PHONG\d{6}$/,
    medical_records: /^HSBA\d{6}$/
  };
  
  const fields = {
    doctors: 'doctor_id',
    patients: 'patient_id',
    appointments: 'appointment_id',
    departments: 'department_id',
    rooms: 'room_id',
    medical_records: 'record_id'
  };
  
  let totalRecords = 0;
  let compliantRecords = 0;
  
  for (const [table, pattern] of Object.entries(vietnamPatterns)) {
    try {
      const { data } = await supabase.from(table).select('*');
      const field = fields[table];
      
      const invalidRecords = data?.filter(record => !pattern.test(record[field])) || [];
      const validCount = (data?.length || 0) - invalidRecords.length;
      
      totalRecords += data?.length || 0;
      compliantRecords += validCount;
      
      if (invalidRecords.length === 0) {
        log('green', `   ✅ ${table}: All ${data?.length || 0} records compliant`);
      } else {
        log('red', `   ❌ ${table}: ${invalidRecords.length} non-compliant`);
      }
    } catch (error) {
      log('red', `   ❌ ${table}: Verification failed`);
    }
  }
  
  // Check license number format
  log('blue', '\n🔍 Checking license number format (VN-XX-1234)...');
  const { data: doctorsForLicense } = await supabase.from('doctors').select('doctor_id, license_number');
  const licensePattern = /^VN-[A-Z]{2,4}-\d{4}$/;
  
  const validLicenses = doctorsForLicense?.filter(doctor => 
    licensePattern.test(doctor.license_number)
  ).length || 0;
  
  log('green', `   ✅ ${validLicenses}/${doctorsForLicense?.length || 0} license numbers compliant`);
  
  const complianceRate = ((compliantRecords / totalRecords) * 100).toFixed(1);
  
  log('cyan', `\n📊 VIETNAM FORMAT COMPLIANCE`);
  log('cyan', `============================`);
  log('blue', `Total Records: ${totalRecords}`);
  log('green', `Compliant Records: ${compliantRecords}`);
  log('magenta', `Compliance Rate: ${complianceRate}%`);
  
  return complianceRate >= 95;
}

async function main() {
  log('cyan', '🇻🇳 Hospital Management - Vietnam Format Migration');
  log('cyan', '==================================================');
  
  try {
    const backupFile = await createBackup();
    await vietnamMigration();
    const success = await verifyVietnamFormat();
    
    if (success) {
      log('green', '\n🎉 Vietnam migration completed successfully!');
      log('blue', `📦 Backup: ${backupFile}`);
      log('cyan', '\n✅ New Vietnam format applied:');
      log('yellow', '   - DOC000001 for doctors');
      log('yellow', '   - BN000001 for patients (Bệnh nhân)');
      log('yellow', '   - LK000001 for appointments (Lịch khám)');
      log('yellow', '   - KHOA000001 for departments');
      log('yellow', '   - PHONG000001 for rooms');
      log('yellow', '   - HSBA000001 for medical records');
      log('yellow', '   - VN-XX-1234 for license numbers');
    } else {
      log('yellow', '\n⚠️  Migration completed with some issues.');
    }
    
  } catch (error) {
    log('red', `❌ Migration failed: ${error.message}`);
    process.exit(1);
  }
}

main();
