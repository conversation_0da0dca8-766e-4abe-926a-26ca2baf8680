const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

function log(color, message) {
  const colors = {
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m',
    reset: '\x1b[0m'
  };
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// English to Vietnamese specialty mapping
const SPECIALTY_MAPPING = {
  'tim_mach': 'cardiology',
  'than_kinh': 'neurology',
  'nhi_khoa': 'pediatrics',
  'san_phu_khoa': 'obstetrics_gynecology',
  'ngoai_khoa': 'surgery',
  'noi_khoa': 'internal_medicine',
  'mat': 'ophthalmology',
  'tai_mui_hong': 'ent',
  'da_lieu': 'dermatology',
  'tam_than': 'psychiatry',
  'ung_buou': 'oncology',
  'gay_me_hoi_suc': 'anesthesiology',
  'chan_doan_hinh_anh': 'radiology',
  'xet_nghiem': 'laboratory_medicine',
  'cap_cuu': 'emergency_medicine'
};

// Status mapping Vietnamese to English
const STATUS_MAPPING = {
  'dang_lam': 'active',
  'nghi_phep': 'on_leave',
  'tam_nghi': 'inactive',
  'nghi_huu': 'retired',
  'dang_dieu_tri': 'active',
  'xuat_vien': 'discharged',
  'chuyen_vien': 'transferred',
  'tu_vong': 'deceased',
  'dat_lich': 'scheduled',
  'xac_nhan': 'confirmed',
  'dang_kham': 'in_progress',
  'hoan_thanh': 'completed',
  'huy_bo': 'cancelled',
  'vang_mat': 'no_show'
};

// Room type mapping
const ROOM_TYPE_MAPPING = {
  'phong_kham': 'consultation',
  'phong_mo': 'surgery',
  'phong_cap_cuu': 'emergency',
  'phong_benh': 'ward',
  'phong_hoi_suc': 'icu',
  'phong_xet_nghiem': 'laboratory',
  'phong_chan_doan_hinh_anh': 'radiology',
  'phong_thuoc': 'pharmacy',
  'phong_phuc_hoi_chuc_nang': 'rehabilitation'
};

async function createBackup() {
  log('cyan', '📦 Creating backup for bilingual migration...');
  
  try {
    const tables = ['doctors', 'patients', 'appointments', 'departments', 'rooms', 'medical_records'];
    const backup = {
      timestamp: new Date().toISOString(),
      migration_type: 'bilingual_vietnam',
      tables: {}
    };
    
    for (const table of tables) {
      const { data, error } = await supabase.from(table).select('*');
      if (error) {
        log('yellow', `⚠️  Could not backup ${table}: ${error.message}`);
        backup.tables[table] = [];
      } else {
        backup.tables[table] = data || [];
        log('blue', `   ✅ ${table}: ${data?.length || 0} records`);
      }
    }
    
    const fs = require('fs');
    const backupFile = `bilingual-backup-${Date.now()}.json`;
    fs.writeFileSync(backupFile, JSON.stringify(backup, null, 2));
    
    log('green', `✅ Backup created: ${backupFile}`);
    return backupFile;
  } catch (error) {
    log('red', `❌ Backup failed: ${error.message}`);
    throw error;
  }
}

async function createTranslationTables() {
  log('cyan', '\n🌐 Creating translation tables...');
  
  try {
    // Create field translations table
    const { error: createError } = await supabase.rpc('create_translation_table', {
      sql: `
        CREATE TABLE IF NOT EXISTS field_translations (
          field_key VARCHAR(100) PRIMARY KEY,
          vietnamese_label VARCHAR(200),
          english_label VARCHAR(200),
          description TEXT,
          created_at TIMESTAMP DEFAULT NOW()
        );
      `
    });
    
    if (createError) {
      log('yellow', `⚠️  Translation table may already exist: ${createError.message}`);
    } else {
      log('green', '✅ Translation table created');
    }
    
    // Insert field translations
    const translations = [
      { field_key: 'doctor_id', vietnamese_label: 'Mã bác sĩ', english_label: 'Doctor ID' },
      { field_key: 'patient_id', vietnamese_label: 'Mã bệnh nhân', english_label: 'Patient ID' },
      { field_key: 'full_name', vietnamese_label: 'Họ và tên', english_label: 'Full Name' },
      { field_key: 'email', vietnamese_label: 'Email', english_label: 'Email' },
      { field_key: 'phone_number', vietnamese_label: 'Số điện thoại', english_label: 'Phone Number' },
      { field_key: 'license_number', vietnamese_label: 'Số bằng cấp', english_label: 'License Number' },
      { field_key: 'specialty', vietnamese_label: 'Chuyên khoa', english_label: 'Specialty' },
      { field_key: 'status', vietnamese_label: 'Trạng thái', english_label: 'Status' },
      { field_key: 'gender', vietnamese_label: 'Giới tính', english_label: 'Gender' },
      { field_key: 'blood_type', vietnamese_label: 'Nhóm máu', english_label: 'Blood Type' },
      { field_key: 'appointment_date', vietnamese_label: 'Ngày khám', english_label: 'Appointment Date' },
      { field_key: 'room_type', vietnamese_label: 'Loại phòng', english_label: 'Room Type' }
    ];
    
    for (const translation of translations) {
      const { error } = await supabase
        .from('field_translations')
        .upsert(translation, { onConflict: 'field_key' });
        
      if (error) {
        log('yellow', `⚠️  Could not insert translation for ${translation.field_key}`);
      }
    }
    
    log('green', `✅ Inserted ${translations.length} field translations`);
    
  } catch (error) {
    log('red', `❌ Translation table creation failed: ${error.message}`);
  }
}

async function updateDoctorSpecialties() {
  log('cyan', '\n👨‍⚕️ Converting doctor specialties to English...');
  
  try {
    const { data: doctors } = await supabase.from('doctors').select('*');
    
    for (const doctor of doctors) {
      let updates = {};
      let needsUpdate = false;
      
      // Convert specialty to English if it's in Vietnamese
      if (doctor.specialty && SPECIALTY_MAPPING[doctor.specialty]) {
        updates.specialty = SPECIALTY_MAPPING[doctor.specialty];
        needsUpdate = true;
        log('yellow', `   ${doctor.doctor_id}: ${doctor.specialty} → ${updates.specialty}`);
      }
      
      // Convert status to English if it's in Vietnamese
      if (doctor.status && STATUS_MAPPING[doctor.status]) {
        updates.status = STATUS_MAPPING[doctor.status];
        needsUpdate = true;
        log('yellow', `   ${doctor.doctor_id}: status ${doctor.status} → ${updates.status}`);
      }
      
      if (needsUpdate) {
        const { error } = await supabase
          .from('doctors')
          .update(updates)
          .eq('doctor_id', doctor.doctor_id);
          
        if (error) {
          log('red', `❌ Failed to update doctor ${doctor.doctor_id}: ${error.message}`);
        } else {
          log('green', `✅ Updated doctor ${doctor.doctor_id}`);
        }
      }
    }
    
  } catch (error) {
    log('red', `❌ Doctor specialty update failed: ${error.message}`);
  }
}

async function updatePatientStatuses() {
  log('cyan', '\n🏥 Converting patient statuses to English...');
  
  try {
    const { data: patients } = await supabase.from('patients').select('*');
    
    for (const patient of patients) {
      let updates = {};
      let needsUpdate = false;
      
      // Convert status to English if it's in Vietnamese
      if (patient.status && STATUS_MAPPING[patient.status]) {
        updates.status = STATUS_MAPPING[patient.status];
        needsUpdate = true;
        log('yellow', `   ${patient.patient_id}: ${patient.status} → ${updates.status}`);
      }
      
      // Convert gender to English if needed
      if (patient.gender) {
        const genderMapping = { 'nam': 'male', 'nu': 'female', 'khac': 'other' };
        if (genderMapping[patient.gender]) {
          updates.gender = genderMapping[patient.gender];
          needsUpdate = true;
          log('yellow', `   ${patient.patient_id}: gender ${patient.gender} → ${updates.gender}`);
        }
      }
      
      if (needsUpdate) {
        const { error } = await supabase
          .from('patients')
          .update(updates)
          .eq('patient_id', patient.patient_id);
          
        if (error) {
          log('red', `❌ Failed to update patient ${patient.patient_id}: ${error.message}`);
        } else {
          log('green', `✅ Updated patient ${patient.patient_id}`);
        }
      }
    }
    
  } catch (error) {
    log('red', `❌ Patient status update failed: ${error.message}`);
  }
}

async function updateAppointmentStatuses() {
  log('cyan', '\n📅 Converting appointment statuses to English...');
  
  try {
    const { data: appointments } = await supabase.from('appointments').select('*');
    
    for (const appointment of appointments) {
      let updates = {};
      let needsUpdate = false;
      
      // Convert status to English if it's in Vietnamese
      if (appointment.status && STATUS_MAPPING[appointment.status]) {
        updates.status = STATUS_MAPPING[appointment.status];
        needsUpdate = true;
        log('yellow', `   ${appointment.appointment_id}: ${appointment.status} → ${updates.status}`);
      }
      
      // Convert type to English if needed
      if (appointment.type) {
        const typeMapping = { 
          'kham_benh': 'consultation', 
          'tai_kham': 'follow_up', 
          'cap_cuu': 'emergency', 
          'phau_thuat': 'surgery' 
        };
        if (typeMapping[appointment.type]) {
          updates.type = typeMapping[appointment.type];
          needsUpdate = true;
          log('yellow', `   ${appointment.appointment_id}: type ${appointment.type} → ${updates.type}`);
        }
      }
      
      if (needsUpdate) {
        const { error } = await supabase
          .from('appointments')
          .update(updates)
          .eq('appointment_id', appointment.appointment_id);
          
        if (error) {
          log('red', `❌ Failed to update appointment ${appointment.appointment_id}: ${error.message}`);
        } else {
          log('green', `✅ Updated appointment ${appointment.appointment_id}`);
        }
      }
    }
    
  } catch (error) {
    log('red', `❌ Appointment status update failed: ${error.message}`);
  }
}

async function updateRoomTypes() {
  log('cyan', '\n🏠 Converting room types to English...');
  
  try {
    const { data: rooms } = await supabase.from('rooms').select('*');
    
    for (const room of rooms) {
      let updates = {};
      let needsUpdate = false;
      
      // Convert room type to English if it's in Vietnamese
      if (room.room_type && ROOM_TYPE_MAPPING[room.room_type]) {
        updates.room_type = ROOM_TYPE_MAPPING[room.room_type];
        needsUpdate = true;
        log('yellow', `   ${room.room_id}: ${room.room_type} → ${updates.room_type}`);
      }
      
      // Convert status to English if needed
      if (room.status) {
        const statusMapping = { 
          'san_sang': 'available', 
          'dang_su_dung': 'occupied', 
          'bao_tri': 'maintenance', 
          'ngung_hoat_dong': 'out_of_service' 
        };
        if (statusMapping[room.status]) {
          updates.status = statusMapping[room.status];
          needsUpdate = true;
          log('yellow', `   ${room.room_id}: status ${room.status} → ${updates.status}`);
        }
      }
      
      if (needsUpdate) {
        const { error } = await supabase
          .from('rooms')
          .update(updates)
          .eq('room_id', room.room_id);
          
        if (error) {
          log('red', `❌ Failed to update room ${room.room_id}: ${error.message}`);
        } else {
          log('green', `✅ Updated room ${room.room_id}`);
        }
      }
    }
    
  } catch (error) {
    log('red', `❌ Room type update failed: ${error.message}`);
  }
}

async function verifyBilingualImplementation() {
  log('cyan', '\n🔍 Verifying bilingual implementation...');
  
  try {
    // Check if all specialties are in English
    const { data: doctors } = await supabase.from('doctors').select('specialty');
    const englishSpecialties = Object.values(SPECIALTY_MAPPING);
    const invalidSpecialties = doctors?.filter(d => 
      d.specialty && !englishSpecialties.includes(d.specialty)
    ) || [];
    
    if (invalidSpecialties.length === 0) {
      log('green', `   ✅ All ${doctors?.length || 0} doctor specialties are in English`);
    } else {
      log('red', `   ❌ ${invalidSpecialties.length} doctors have non-English specialties`);
    }
    
    // Check if all statuses are in English
    const englishStatuses = Object.values(STATUS_MAPPING);
    const { data: patients } = await supabase.from('patients').select('status');
    const invalidPatientStatuses = patients?.filter(p => 
      p.status && !englishStatuses.includes(p.status)
    ) || [];
    
    if (invalidPatientStatuses.length === 0) {
      log('green', `   ✅ All ${patients?.length || 0} patient statuses are in English`);
    } else {
      log('red', `   ❌ ${invalidPatientStatuses.length} patients have non-English statuses`);
    }
    
    // Check translation table
    const { data: translations } = await supabase.from('field_translations').select('*');
    if (translations && translations.length > 0) {
      log('green', `   ✅ Translation table has ${translations.length} entries`);
    } else {
      log('red', `   ❌ Translation table is empty or missing`);
    }
    
    const totalIssues = invalidSpecialties.length + invalidPatientStatuses.length;
    const complianceRate = ((1 - totalIssues / ((doctors?.length || 0) + (patients?.length || 0))) * 100).toFixed(1);
    
    log('cyan', `\n📊 BILINGUAL COMPLIANCE REPORT`);
    log('cyan', `==============================`);
    log('blue', `English Technical Fields: ${complianceRate}% compliant`);
    log('green', `Translation System: ${translations?.length || 0} field mappings`);
    log('magenta', `Ready for Vietnamese UI display`);
    
    return complianceRate >= 95;
    
  } catch (error) {
    log('red', `❌ Verification failed: ${error.message}`);
    return false;
  }
}

async function main() {
  log('cyan', '🇻🇳🇺🇸 Hospital Management - Bilingual Migration');
  log('cyan', '================================================');
  
  try {
    const backupFile = await createBackup();
    await createTranslationTables();
    await updateDoctorSpecialties();
    await updatePatientStatuses();
    await updateAppointmentStatuses();
    await updateRoomTypes();
    const success = await verifyBilingualImplementation();
    
    if (success) {
      log('green', '\n🎉 Bilingual migration completed successfully!');
      log('blue', `📦 Backup: ${backupFile}`);
      log('cyan', '\n✅ Bilingual system implemented:');
      log('yellow', '   - English for technical fields (database)');
      log('yellow', '   - Vietnamese for user interface (display)');
      log('yellow', '   - Translation mapping system created');
      log('yellow', '   - Field labels in both languages');
      log('yellow', '   - Medical terminology standardized');
    } else {
      log('yellow', '\n⚠️  Migration completed with some issues.');
    }
    
  } catch (error) {
    log('red', `❌ Migration failed: ${error.message}`);
    process.exit(1);
  }
}

main();
