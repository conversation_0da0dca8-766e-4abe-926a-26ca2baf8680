{"name": "@hospital/doctor-service", "version": "1.0.0", "description": "Doctor Management Service", "main": "dist/index.js", "scripts": {"start": "node dist/index.js", "dev": "nodemon src/index.ts", "build": "tsc", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/**/*.ts", "format": "prettier --write src/**/*.ts"}, "dependencies": {"@hospital/shared": "file:../../shared", "@supabase/supabase-js": "^2.38.0", "express": "^4.18.2", "express-validator": "^7.0.1", "cors": "^2.8.5", "helmet": "^7.0.0", "morgan": "^1.10.0", "dotenv": "^16.3.0", "zod": "^3.21.4", "uuid": "^9.0.0", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.0"}, "devDependencies": {"@types/express": "^4.17.17", "@types/cors": "^2.8.13", "@types/morgan": "^1.9.4", "@types/node": "^20.4.0", "@types/uuid": "^9.0.0", "@types/swagger-jsdoc": "^6.0.1", "@types/swagger-ui-express": "^4.1.3", "typescript": "^5.1.0", "nodemon": "^3.0.1", "ts-node": "^10.9.1", "jest": "^29.6.0", "@types/jest": "^29.5.3", "ts-jest": "^29.1.1"}}