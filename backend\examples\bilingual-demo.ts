// ============================================================================
// BILINGUAL SYSTEM DEMO - VIETNAM HOSPITAL MANAGEMENT
// ============================================================================

import { translationService } from '../shared/src/services/translation.service';

// ============================================================================
// SAMPLE DATA (English - Database Format)
// ============================================================================

const sampleDoctor = {
  doctor_id: 'DOC000001',
  full_name: 'Dr. Nguyễn Văn An',
  email: '<EMAIL>',
  phone_number: '**********',
  license_number: 'VN-BS-1234',
  specialty: 'cardiology',           // English in database
  experience_years: 10,
  consultation_fee: 500000,
  department_id: 'DEPT000001',
  status: 'active',                  // English in database
  working_hours: {
    monday: '08:00-17:00',
    tuesday: '08:00-17:00',
    wednesday: '08:00-17:00',
    thursday: '08:00-17:00',
    friday: '08:00-17:00',
    saturday: '08:00-12:00',
    sunday: 'off'
  },
  created_at: '2025-01-30T10:30:00Z',
  updated_at: '2025-01-30T10:30:00Z'
};

const samplePatient = {
  patient_id: 'PAT000001',
  full_name: 'Trần Thị Bình',
  email: '<EMAIL>',
  phone_number: '**********',
  date_of_birth: '1985-05-15',
  gender: 'female',                  // English in database
  blood_type: 'A+',
  address: {
    so_nha: '123',
    duong: 'Nguyễn Văn Cừ',
    phuong_xa: 'Phường 4',
    quan_huyen: 'Quận 5',
    tinh_tp: 'TP. Hồ Chí Minh'
  },
  insurance_info: {
    type: 'bhyt',
    number: 'HS4020123456789',
    valid_until: '2025-12-31'
  },
  status: 'active',                  // English in database
  created_at: '2025-01-30T09:15:00Z'
};

const sampleAppointment = {
  appointment_id: 'APT000001',
  patient_id: 'PAT000001',
  doctor_id: 'DOC000001',
  appointment_date: '2025-02-01',
  appointment_time: '14:30:00',
  duration_minutes: 30,
  type: 'consultation',              // English in database
  status: 'scheduled',               // English in database
  room_id: 'ROOM000001',
  notes: 'Khám định kỳ tim mạch',
  created_at: '2025-01-30T11:00:00Z'
};

const sampleRoom = {
  room_id: 'ROOM000001',
  room_number: 'P101',
  room_type: 'consultation',         // English in database
  department_id: 'DEPT000001',
  capacity: 1,
  equipment: ['ECG machine', 'Blood pressure monitor', 'Stethoscope'],
  status: 'available',               // English in database
  created_at: '2025-01-30T08:00:00Z'
};

// ============================================================================
// DEMO FUNCTIONS
// ============================================================================

function demonstrateBilingualSystem() {
  console.log('🇻🇳🇺🇸 BILINGUAL HOSPITAL MANAGEMENT SYSTEM DEMO');
  console.log('=================================================\n');

  // 1. Doctor Information Display
  console.log('👨‍⚕️ DOCTOR INFORMATION');
  console.log('=====================');
  console.log('📊 Database Format (English):');
  console.log(`   Specialty: ${sampleDoctor.specialty}`);
  console.log(`   Status: ${sampleDoctor.status}`);
  console.log(`   Fee: ${sampleDoctor.consultation_fee}`);
  
  console.log('\n🇻🇳 Vietnamese UI Display:');
  const doctorDisplay = translationService.formatForDisplay(sampleDoctor);
  console.log(`   Chuyên khoa: ${doctorDisplay.specialty_display}`);
  console.log(`   Trạng thái: ${doctorDisplay.status_display}`);
  console.log(`   Phí khám: ${doctorDisplay.consultation_fee_display}`);
  console.log(`   Ngày tạo: ${doctorDisplay.created_at_display}`);

  // 2. Patient Information Display
  console.log('\n🏥 PATIENT INFORMATION');
  console.log('======================');
  console.log('📊 Database Format (English):');
  console.log(`   Gender: ${samplePatient.gender}`);
  console.log(`   Status: ${samplePatient.status}`);
  
  console.log('\n🇻🇳 Vietnamese UI Display:');
  const patientDisplay = translationService.formatForDisplay(samplePatient);
  console.log(`   Giới tính: ${translationService.translateStatus(samplePatient.gender)}`);
  console.log(`   Trạng thái: ${patientDisplay.status_display}`);
  console.log(`   Ngày tạo: ${patientDisplay.created_at_display}`);

  // 3. Appointment Information Display
  console.log('\n📅 APPOINTMENT INFORMATION');
  console.log('==========================');
  console.log('📊 Database Format (English):');
  console.log(`   Type: ${sampleAppointment.type}`);
  console.log(`   Status: ${sampleAppointment.status}`);
  
  console.log('\n🇻🇳 Vietnamese UI Display:');
  const appointmentDisplay = translationService.formatForDisplay(sampleAppointment);
  console.log(`   Loại khám: ${appointmentDisplay.type_display}`);
  console.log(`   Trạng thái: ${appointmentDisplay.status_display}`);
  console.log(`   Ngày tạo: ${appointmentDisplay.created_at_display}`);

  // 4. Room Information Display
  console.log('\n🏠 ROOM INFORMATION');
  console.log('===================');
  console.log('📊 Database Format (English):');
  console.log(`   Type: ${sampleRoom.room_type}`);
  console.log(`   Status: ${sampleRoom.status}`);
  
  console.log('\n🇻🇳 Vietnamese UI Display:');
  const roomDisplay = translationService.formatForDisplay(sampleRoom);
  console.log(`   Loại phòng: ${roomDisplay.room_type_display}`);
  console.log(`   Trạng thái: ${roomDisplay.status_display}`);
  console.log(`   Ngày tạo: ${roomDisplay.created_at_display}`);
}

function demonstrateDropdownOptions() {
  console.log('\n📋 DROPDOWN OPTIONS FOR VIETNAMESE UI');
  console.log('=====================================');

  // Specialty options
  console.log('\n🏥 Chuyên khoa (Specialties):');
  const specialtyOptions = translationService.getSpecialtyOptions();
  specialtyOptions.slice(0, 5).forEach(option => {
    console.log(`   ${option.value} → ${option.label}`);
  });
  console.log(`   ... and ${specialtyOptions.length - 5} more`);

  // Status options
  console.log('\n👨‍⚕️ Trạng thái bác sĩ (Doctor Status):');
  const doctorStatusOptions = translationService.getStatusOptions('doctor');
  doctorStatusOptions.forEach(option => {
    console.log(`   ${option.value} → ${option.label}`);
  });

  // Room type options
  console.log('\n🏠 Loại phòng (Room Types):');
  const roomTypeOptions = translationService.getRoomTypeOptions();
  roomTypeOptions.slice(0, 5).forEach(option => {
    console.log(`   ${option.value} → ${option.label}`);
  });

  // Payment method options
  console.log('\n💰 Phương thức thanh toán (Payment Methods):');
  const paymentOptions = translationService.getPaymentMethodOptions();
  paymentOptions.forEach(option => {
    console.log(`   ${option.value} → ${option.label}`);
  });
}

function demonstrateFormLabels() {
  console.log('\n📝 FORM LABELS IN VIETNAMESE');
  console.log('=============================');

  const commonFields = [
    'doctor_id', 'full_name', 'email', 'phone_number', 
    'license_number', 'specialty', 'status', 'consultation_fee'
  ];

  commonFields.forEach(field => {
    const vietnameseLabel = translationService.getFieldLabel(field);
    console.log(`   ${field} → ${vietnameseLabel}`);
  });
}

function demonstrateAPIResponse() {
  console.log('\n🌐 API RESPONSE TRANSFORMATION');
  console.log('===============================');

  // Simulate API response (English)
  const apiResponse = {
    success: true,
    data: {
      doctor_id: 'DOC000001',
      specialty: 'cardiology',
      status: 'active',
      consultation_fee: 500000,
      created_at: '2025-01-30T10:30:00Z'
    },
    message: 'Doctor retrieved successfully'
  };

  console.log('📊 Original API Response (English):');
  console.log(JSON.stringify(apiResponse, null, 2));

  // Transform for Vietnamese UI
  const vietnameseResponse = {
    ...apiResponse,
    data: translationService.formatForDisplay(apiResponse.data),
    message_vn: 'Lấy thông tin bác sĩ thành công'
  };

  console.log('\n🇻🇳 Transformed for Vietnamese UI:');
  console.log(JSON.stringify(vietnameseResponse, null, 2));
}

function demonstrateMedicalTerms() {
  console.log('\n🩺 MEDICAL TERMINOLOGY TRANSLATION');
  console.log('===================================');

  const medicalTerms = [
    'hypertension', 'diabetes_type_2', 'pneumonia', 'covid_19',
    'fever', 'cough', 'chest_pain', 'headache'
  ];

  console.log('🔬 Diagnosis & Symptoms:');
  medicalTerms.forEach(term => {
    const vietnamese = translationService.translateMedicalTerm(term);
    console.log(`   ${term} → ${vietnamese}`);
  });

  const medications = [
    'paracetamol', 'ibuprofen', 'amoxicillin', 'amlodipine'
  ];

  console.log('\n💊 Medications:');
  medications.forEach(med => {
    const vietnamese = translationService.translateMedicalTerm(med);
    console.log(`   ${med} → ${vietnamese}`);
  });
}

function demonstrateValidationMessages() {
  console.log('\n⚠️  VALIDATION MESSAGES');
  console.log('========================');

  const validationErrors = {
    'VALIDATION_ERROR': 'Dữ liệu không hợp lệ',
    'DUPLICATE_EMAIL': 'Email này đã được sử dụng',
    'INVALID_PHONE': 'Số điện thoại phải có 10 chữ số và bắt đầu bằng 0',
    'INVALID_LICENSE': 'Số bằng cấp phải có định dạng VN-XX-1234',
    'REQUIRED_FIELD': 'Trường này là bắt buộc'
  };

  console.log('🇺🇸 English (API Error Codes) → 🇻🇳 Vietnamese (User Messages):');
  Object.entries(validationErrors).forEach(([code, message]) => {
    console.log(`   ${code} → ${message}`);
  });
}

// ============================================================================
// RUN DEMO
// ============================================================================

function runBilingualDemo() {
  demonstrateBilingualSystem();
  demonstrateDropdownOptions();
  demonstrateFormLabels();
  demonstrateAPIResponse();
  demonstrateMedicalTerms();
  demonstrateValidationMessages();

  console.log('\n🎯 SUMMARY');
  console.log('===========');
  console.log('✅ Database stores technical data in English');
  console.log('✅ UI displays everything in Vietnamese');
  console.log('✅ Translation service handles all conversions');
  console.log('✅ Form labels, dropdowns, and messages in Vietnamese');
  console.log('✅ Medical terminology properly translated');
  console.log('✅ Date/time/currency formatted for Vietnam');
  console.log('✅ API responses can be transformed for UI');
  console.log('\n🇻🇳 Perfect for Vietnam healthcare market! 🏥');
}

// Export for use in other files
export {
  demonstrateBilingualSystem,
  demonstrateDropdownOptions,
  demonstrateFormLabels,
  demonstrateAPIResponse,
  demonstrateMedicalTerms,
  demonstrateValidationMessages,
  runBilingualDemo
};

// Run demo if this file is executed directly
if (require.main === module) {
  runBilingualDemo();
}
