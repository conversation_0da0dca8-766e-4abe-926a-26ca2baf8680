# 🎯 Hospital Management System - Unified Format Standards

## 📋 **OVERVIEW**

<PERSON><PERSON><PERSON> là bộ chuẩn format thống nhất cho toàn bộ hệ thống Hospital Management để đảm bảo:
- ✅ **Consistency** - T<PERSON>h nhất quán across all tables
- ✅ **Scalability** - K<PERSON><PERSON> năng mở rộng trong tương lai
- ✅ **Maintainability** - <PERSON><PERSON> bảo trì và debug
- ✅ **International Standards** - <PERSON><PERSON> thủ chuẩn quốc tế

## 🔑 **1. PRIMARY KEY STANDARDS**

### **Format Pattern**: `{PREFIX}{YYYYMMDD}{SEQUENCE}`
- **PREFIX**: 3-4 ký tự viết hoa
- **YYYYMMDD**: <PERSON><PERSON><PERSON> tạo (8 chữ số)
- **SEQUENCE**: S<PERSON> thứ tự trong ngày (4 chữ số)

### **Examples**:
```
DOC20250118001  // Doctor created on 2025-01-18, first of the day
PAT20250118001  // Patient created on 2025-01-18, first of the day
APT20250118001  // Appointment created on 2025-01-18, first of the day
```

### **Table-Specific Prefixes**:
| Table | Prefix | Example | Description |
|-------|--------|---------|-------------|
| **profiles** | `USR` | `USR20250118001` | User profiles |
| **doctors** | `DOC` | `DOC20250118001` | Doctor records |
| **patients** | `PAT` | `PAT20250118001` | Patient records |
| **appointments** | `APT` | `APT20250118001` | Appointments |
| **departments** | `DEPT` | `DEPT20250118001` | Departments |
| **rooms** | `ROOM` | `ROOM20250118001` | Hospital rooms |
| **medical_records** | `MED` | `MED20250118001` | Medical records |
| **prescriptions** | `PRESC` | `PRESC20250118001` | Prescriptions |
| **billing** | `BILL` | `BILL20250118001` | Billing records |
| **payments** | `PAY` | `PAY20250118001` | Payment records |
| **schedules** | `SCHED` | `SCHED20250118001` | Doctor schedules |
| **availability** | `AVAIL` | `AVAIL20250118001` | Doctor availability |

## 🏥 **2. MEDICAL IDENTIFICATION STANDARDS**

### **License Numbers**: `{COUNTRY}{TYPE}{SEQUENCE}`
```
VN-MD-123456    // Vietnam Medical Doctor
VN-BS-123456    // Vietnam Bachelor of Surgery
VN-DDS-123456   // Vietnam Doctor of Dental Surgery
VN-RN-123456    // Vietnam Registered Nurse
```

### **Patient Medical Numbers**: `{HOSPITAL}{YEAR}{SEQUENCE}`
```
HMC-2025-000001  // Hospital Medical Center, year 2025, patient #1
```

### **Insurance Policy Numbers**: `{PROVIDER}-{TYPE}-{SEQUENCE}`
```
BHYT-STD-123456789    // BHYT Standard policy
BHXH-PRM-123456789    // BHXH Premium policy
```

## 📞 **3. CONTACT INFORMATION STANDARDS**

### **Phone Numbers**: International E.164 Format
```
+84901234567     // Vietnam mobile (preferred)
+842812345678    // Vietnam landline
028-1234-5678    // Local format (alternative)
```

### **Email Addresses**: RFC 5322 Compliant
```
<EMAIL>
<EMAIL>
<EMAIL>
```

### **Addresses**: Structured JSON Format
```json
{
  "street": "123 Nguyễn Văn Cừ",
  "ward": "Phường 4",
  "district": "Quận 5",
  "city": "TP. Hồ Chí Minh",
  "province": "TP. Hồ Chí Minh",
  "postal_code": "70000",
  "country": "Vietnam",
  "coordinates": {
    "latitude": 10.7769,
    "longitude": 106.7009
  }
}
```

## 🩺 **4. MEDICAL DATA STANDARDS**

### **Blood Types**: ABO + Rh System
```
A+, A-, B+, B-, AB+, AB-, O+, O-
```

### **Vital Signs**: International Units
```json
{
  "temperature": 36.5,        // Celsius (°C)
  "blood_pressure": {
    "systolic": 120,          // mmHg
    "diastolic": 80           // mmHg
  },
  "heart_rate": 72,           // beats per minute (bpm)
  "respiratory_rate": 16,     // breaths per minute
  "oxygen_saturation": 98,    // percentage (%)
  "weight": 70.5,             // kilograms (kg)
  "height": 175.0,            // centimeters (cm)
  "bmi": 23.0                 // kg/m²
}
```

### **Medication Dosages**: Standard Medical Format
```json
{
  "name": "Amlodipine",
  "strength": "5mg",
  "form": "tablet",
  "dosage": "1 tablet",
  "frequency": "once daily",
  "route": "oral",
  "duration": "30 days",
  "instructions": "Take with food"
}
```

## ⏰ **5. DATE & TIME STANDARDS**

### **Date Format**: ISO 8601
```
2025-01-18              // Date only
2025-01-18T14:30:00Z    // UTC timestamp
2025-01-18T14:30:00+07:00  // Vietnam timezone
```

### **Time Ranges**: 24-hour format
```
07:00-16:00    // Working hours
14:30-15:00    // Appointment slot
```

### **Schedule Format**: Structured JSON
```json
{
  "monday": {
    "available": true,
    "shifts": [
      {"start": "08:00", "end": "12:00", "type": "morning"},
      {"start": "13:00", "end": "17:00", "type": "afternoon"}
    ]
  },
  "tuesday": {
    "available": true,
    "shifts": [
      {"start": "08:00", "end": "17:00", "type": "full_day"}
    ]
  },
  "sunday": {
    "available": false,
    "reason": "weekend"
  }
}
```

## 💰 **6. FINANCIAL DATA STANDARDS**

### **Currency**: Vietnamese Dong (VND)
```json
{
  "amount": 500000,           // Always in VND, no decimals
  "currency": "VND",
  "formatted": "500,000 ₫"
}
```

### **Billing Codes**: International Classification
```
ICD-10: I10        // Essential hypertension
CPT: 99213         // Office visit, established patient
DRG: 194           // Simple pneumonia
```

## 🏷️ **7. STATUS & ENUM STANDARDS**

### **Universal Status Values**:
```
active, inactive, pending, completed, cancelled, expired, archived
```

### **Medical-Specific Status**:
```
// Appointment Status
scheduled, confirmed, in_progress, completed, cancelled, no_show, rescheduled

// Prescription Status
prescribed, dispensed, partially_dispensed, cancelled, expired

// Medical Record Status
draft, active, amended, archived, deleted

// Patient Status
active, inactive, deceased, transferred

// Doctor Status
active, inactive, on_leave, suspended, retired
```

## 🔐 **8. SECURITY & PRIVACY STANDARDS**

### **Sensitive Data Encryption**:
- Medical records: AES-256 encryption
- Personal information: Field-level encryption
- Audit logs: Immutable with digital signatures

### **Access Control Levels**:
```
public, internal, confidential, restricted, top_secret
```

### **Data Retention Policies**:
```json
{
  "medical_records": "lifetime + 30 years",
  "appointments": "7 years",
  "billing": "10 years",
  "audit_logs": "permanent",
  "user_sessions": "30 days"
}
```

## 📊 **9. VALIDATION RULES**

### **Field Length Limits**:
```
names: 2-100 characters
descriptions: 0-2000 characters
notes: 0-5000 characters
codes: 2-20 characters
```

### **Numeric Ranges**:
```
age: 0-150 years
experience: 0-60 years
temperature: 30.0-45.0°C
heart_rate: 30-250 bpm
blood_pressure: 40-300 mmHg
weight: 0.5-500.0 kg
height: 30.0-250.0 cm
```

### **Pattern Validation**:
```regex
email: ^[^\s@]+@[^\s@]+\.[^\s@]+$
phone: ^\+?[0-9\s\-\(\)]{8,15}$
postal_code: ^\d{5,6}$
license: ^[A-Z]{2}-[A-Z]{2,4}-\d{6,10}$
```

## 🌐 **10. INTERNATIONALIZATION STANDARDS**

### **Language Codes**: ISO 639-1
```
vi: Vietnamese (primary)
en: English (secondary)
fr: French (optional)
```

### **Country Codes**: ISO 3166-1
```
VN: Vietnam
US: United States
FR: France
```

### **Timezone**: UTC+7 (Asia/Ho_Chi_Minh)
```
All timestamps stored in UTC
Display in local timezone: UTC+7
```

## 🔄 **11. API RESPONSE STANDARDS**

### **Success Response**:
```json
{
  "success": true,
  "data": {...},
  "meta": {
    "timestamp": "2025-01-18T14:30:00Z",
    "version": "1.0.0",
    "request_id": "req_20250118_001"
  }
}
```

### **Error Response**:
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid format",
    "details": {...}
  },
  "meta": {
    "timestamp": "2025-01-18T14:30:00Z",
    "request_id": "req_20250118_001"
  }
}
```

## 📝 **12. AUDIT & LOGGING STANDARDS**

### **Audit Log Format**:
```json
{
  "id": "AUDIT20250118001",
  "timestamp": "2025-01-18T14:30:00Z",
  "user_id": "USR20250118001",
  "action": "CREATE",
  "resource": "medical_records",
  "resource_id": "MED20250118001",
  "changes": {...},
  "ip_address": "*************",
  "user_agent": "Mozilla/5.0...",
  "session_id": "sess_20250118_001"
}
```

## 🎯 **IMPLEMENTATION PRIORITY**

### **Phase 1 (Critical)**:
1. ✅ Primary key format standardization
2. ✅ Foreign key relationship fixes
3. ✅ Medical identification numbers

### **Phase 2 (Important)**:
1. 🔧 Contact information standardization
2. 🔧 Date/time format consistency
3. 🔧 Status enum standardization

### **Phase 3 (Enhancement)**:
1. 📋 Financial data standards
2. 📋 Internationalization support
3. 📋 Advanced audit logging

## 🚀 **MIGRATION STRATEGY**

### **Current State vs. Proposed Standards**:

| Table | Current Format | Proposed Format | Migration Required |
|-------|----------------|-----------------|-------------------|
| **doctors** | `DOC001`, `DOC1748...` | `DOC20250118001` | ✅ YES |
| **patients** | `PAT1747555777` | `PAT20250118001` | ✅ YES |
| **appointments** | `APT1747555777` | `APT20250118001` | ✅ YES |
| **departments** | `DEPT1747555777` | `DEPT20250118001` | ✅ YES |
| **rooms** | `ROOM1747555777` | `ROOM20250118001` | ✅ YES |
| **medical_records** | `MR1747555777` | `MED20250118001` | ✅ YES |

### **Migration Steps**:

1. **Phase 1**: Backup current data
2. **Phase 2**: Generate new IDs with date-based format
3. **Phase 3**: Update foreign key references
4. **Phase 4**: Implement validation rules
5. **Phase 5**: Test and verify integrity

### **Benefits of New Format**:
- ✅ **Chronological ordering** - Easy to sort by creation date
- ✅ **Collision prevention** - Date + sequence ensures uniqueness
- ✅ **Audit friendly** - Creation date embedded in ID
- ✅ **Scalable** - Supports 9999 records per day per table
- ✅ **Human readable** - Easy to understand and debug

---

**Document Version**: 1.0
**Last Updated**: 2025-01-18
**Next Review**: 2025-02-18
**Approved By**: Development Team
