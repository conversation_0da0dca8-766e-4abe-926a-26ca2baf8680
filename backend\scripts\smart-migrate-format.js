const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

function log(color, message) {
  const colors = {
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m',
    reset: '\x1b[0m'
  };
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// Generate new ID with unified format: {PREFIX}{YYYYMMDD}{SEQUENCE}
function generateUnifiedId(prefix, date = new Date(), sequence = 1) {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const seq = String(sequence).padStart(3, '0');
  
  return `${prefix}${year}${month}${day}${seq}`;
}

async function createBackup() {
  log('cyan', '📦 Creating backup...');
  
  try {
    const tables = ['doctors', 'patients', 'appointments', 'departments', 'rooms', 'medical_records'];
    const backup = {
      timestamp: new Date().toISOString(),
      tables: {}
    };
    
    for (const table of tables) {
      const { data, error } = await supabase.from(table).select('*');
      if (error) {
        log('yellow', `⚠️  Could not backup ${table}: ${error.message}`);
        backup.tables[table] = [];
      } else {
        backup.tables[table] = data || [];
        log('blue', `   ✅ ${table}: ${data?.length || 0} records`);
      }
    }
    
    const fs = require('fs');
    const backupFile = `backup-smart-${Date.now()}.json`;
    fs.writeFileSync(backupFile, JSON.stringify(backup, null, 2));
    
    log('green', `✅ Backup created: ${backupFile}`);
    return backupFile;
  } catch (error) {
    log('red', `❌ Backup failed: ${error.message}`);
    throw error;
  }
}

async function disableForeignKeyChecks() {
  log('cyan', '🔧 Temporarily disabling foreign key checks...');
  
  try {
    // Note: Supabase/PostgreSQL doesn't allow disabling FK checks globally
    // We'll handle this by updating in the correct order
    log('yellow', '   Using smart update order instead of disabling FK checks');
    return true;
  } catch (error) {
    log('red', `❌ Could not disable FK checks: ${error.message}`);
    return false;
  }
}

async function smartMigration() {
  log('cyan', '\n🧠 Starting smart migration with dependency handling...');
  
  try {
    // Step 1: Get all data first
    const { data: doctors } = await supabase.from('doctors').select('*');
    const { data: patients } = await supabase.from('patients').select('*');
    const { data: departments } = await supabase.from('departments').select('*');
    const { data: rooms } = await supabase.from('rooms').select('*');
    const { data: appointments } = await supabase.from('appointments').select('*');
    const { data: medicalRecords } = await supabase.from('medical_records').select('*');
    
    // Step 2: Create ID mappings
    const doctorMapping = {};
    const patientMapping = {};
    const departmentMapping = {};
    const roomMapping = {};
    const appointmentMapping = {};
    const medicalRecordMapping = {};
    
    // Generate new IDs
    doctors.forEach((doctor, i) => {
      doctorMapping[doctor.doctor_id] = generateUnifiedId('DOC', new Date(), i + 1);
    });
    
    patients.forEach((patient, i) => {
      patientMapping[patient.patient_id] = generateUnifiedId('PAT', new Date(), i + 1);
    });
    
    departments.forEach((dept, i) => {
      departmentMapping[dept.department_id] = generateUnifiedId('DEPT', new Date(), i + 1);
    });
    
    rooms.forEach((room, i) => {
      roomMapping[room.room_id] = generateUnifiedId('ROOM', new Date(), i + 1);
    });
    
    appointments.forEach((apt, i) => {
      appointmentMapping[apt.appointment_id] = generateUnifiedId('APT', new Date(), i + 1);
    });
    
    medicalRecords.forEach((record, i) => {
      medicalRecordMapping[record.record_id] = generateUnifiedId('MED', new Date(), i + 1);
    });
    
    log('blue', '📋 ID Mappings created:');
    log('yellow', `   Doctors: ${Object.keys(doctorMapping).length} mappings`);
    log('yellow', `   Patients: ${Object.keys(patientMapping).length} mappings`);
    log('yellow', `   Departments: ${Object.keys(departmentMapping).length} mappings`);
    log('yellow', `   Rooms: ${Object.keys(roomMapping).length} mappings`);
    log('yellow', `   Appointments: ${Object.keys(appointmentMapping).length} mappings`);
    log('yellow', `   Medical Records: ${Object.keys(medicalRecordMapping).length} mappings`);
    
    // Step 3: Delete all dependent records first (to avoid FK constraints)
    log('cyan', '\n🗑️  Temporarily removing dependent records...');
    
    await supabase.from('medical_records').delete().neq('record_id', 'dummy');
    log('yellow', '   ✅ Removed medical records');
    
    await supabase.from('appointments').delete().neq('appointment_id', 'dummy');
    log('yellow', '   ✅ Removed appointments');
    
    await supabase.from('rooms').delete().neq('room_id', 'dummy');
    log('yellow', '   ✅ Removed rooms');
    
    // Step 4: Update core entities (departments, doctors, patients)
    log('cyan', '\n🔄 Updating core entities...');
    
    // Update departments first (no dependencies)
    for (const dept of departments) {
      const newId = departmentMapping[dept.department_id];
      const { error } = await supabase
        .from('departments')
        .update({ department_id: newId })
        .eq('department_id', dept.department_id);
        
      if (error) {
        log('red', `❌ Failed to update department ${dept.department_id}: ${error.message}`);
      } else {
        log('green', `   ✅ ${dept.department_id} → ${newId}`);
      }
    }
    
    // Update doctors with new department IDs
    for (const doctor of doctors) {
      const newId = doctorMapping[doctor.doctor_id];
      const newDeptId = departmentMapping[doctor.department_id] || doctor.department_id;
      
      const { error } = await supabase
        .from('doctors')
        .update({ 
          doctor_id: newId,
          department_id: newDeptId
        })
        .eq('doctor_id', doctor.doctor_id);
        
      if (error) {
        log('red', `❌ Failed to update doctor ${doctor.doctor_id}: ${error.message}`);
      } else {
        log('green', `   ✅ ${doctor.doctor_id} → ${newId}`);
      }
    }
    
    // Update patients (no dependencies)
    for (const patient of patients) {
      const newId = patientMapping[patient.patient_id];
      
      const { error } = await supabase
        .from('patients')
        .update({ patient_id: newId })
        .eq('patient_id', patient.patient_id);
        
      if (error) {
        log('red', `❌ Failed to update patient ${patient.patient_id}: ${error.message}`);
      } else {
        log('green', `   ✅ ${patient.patient_id} → ${newId}`);
      }
    }
    
    // Step 5: Re-insert dependent records with new IDs
    log('cyan', '\n🔄 Re-inserting dependent records with new IDs...');
    
    // Re-insert rooms
    for (const room of rooms) {
      const newRoomData = {
        ...room,
        room_id: roomMapping[room.room_id],
        department_id: departmentMapping[room.department_id] || room.department_id
      };
      
      const { error } = await supabase
        .from('rooms')
        .insert(newRoomData);
        
      if (error) {
        log('red', `❌ Failed to insert room ${room.room_id}: ${error.message}`);
      } else {
        log('green', `   ✅ Re-inserted ${room.room_id} → ${newRoomData.room_id}`);
      }
    }
    
    // Re-insert appointments
    for (const appointment of appointments) {
      const newAppointmentData = {
        ...appointment,
        appointment_id: appointmentMapping[appointment.appointment_id],
        doctor_id: doctorMapping[appointment.doctor_id] || appointment.doctor_id,
        patient_id: patientMapping[appointment.patient_id] || appointment.patient_id,
        room_id: roomMapping[appointment.room_id] || appointment.room_id
      };
      
      const { error } = await supabase
        .from('appointments')
        .insert(newAppointmentData);
        
      if (error) {
        log('red', `❌ Failed to insert appointment ${appointment.appointment_id}: ${error.message}`);
      } else {
        log('green', `   ✅ Re-inserted ${appointment.appointment_id} → ${newAppointmentData.appointment_id}`);
      }
    }
    
    // Re-insert medical records
    for (const record of medicalRecords) {
      const newRecordData = {
        ...record,
        record_id: medicalRecordMapping[record.record_id],
        doctor_id: doctorMapping[record.doctor_id] || record.doctor_id,
        patient_id: patientMapping[record.patient_id] || record.patient_id,
        appointment_id: appointmentMapping[record.appointment_id] || record.appointment_id
      };
      
      const { error } = await supabase
        .from('medical_records')
        .insert(newRecordData);
        
      if (error) {
        log('red', `❌ Failed to insert medical record ${record.record_id}: ${error.message}`);
      } else {
        log('green', `   ✅ Re-inserted ${record.record_id} → ${newRecordData.record_id}`);
      }
    }
    
    return true;
    
  } catch (error) {
    log('red', `❌ Smart migration failed: ${error.message}`);
    throw error;
  }
}

async function verifyMigration() {
  log('cyan', '\n🔍 Verifying migration results...');
  
  const checks = [
    { table: 'doctors', pattern: /^DOC\d{8}\d{3}$/, field: 'doctor_id' },
    { table: 'patients', pattern: /^PAT\d{8}\d{3}$/, field: 'patient_id' },
    { table: 'appointments', pattern: /^APT\d{8}\d{3}$/, field: 'appointment_id' },
    { table: 'departments', pattern: /^DEPT\d{8}\d{3}$/, field: 'department_id' },
    { table: 'rooms', pattern: /^ROOM\d{8}\d{3}$/, field: 'room_id' },
    { table: 'medical_records', pattern: /^MED\d{8}\d{3}$/, field: 'record_id' }
  ];
  
  let allPassed = true;
  let totalRecords = 0;
  let validRecords = 0;
  
  for (const check of checks) {
    try {
      const { data } = await supabase.from(check.table).select('*');
      const invalidIds = data?.filter(record => !check.pattern.test(record[check.field])) || [];
      const validCount = (data?.length || 0) - invalidIds.length;
      
      totalRecords += data?.length || 0;
      validRecords += validCount;
      
      if (invalidIds.length === 0) {
        log('green', `   ✅ ${check.table}: All ${data?.length || 0} records valid`);
      } else {
        log('red', `   ❌ ${check.table}: ${invalidIds.length} invalid, ${validCount} valid`);
        allPassed = false;
      }
    } catch (error) {
      log('red', `   ❌ ${check.table}: Verification failed - ${error.message}`);
      allPassed = false;
    }
  }
  
  log('cyan', `\n📊 Overall Results: ${validRecords}/${totalRecords} records (${((validRecords/totalRecords)*100).toFixed(1)}%) compliant`);
  
  return allPassed;
}

async function main() {
  log('cyan', '🧠 Hospital Management - Smart Format Migration');
  log('cyan', '===============================================');
  
  try {
    // Step 1: Backup
    const backupFile = await createBackup();
    
    // Step 2: Smart migration with dependency handling
    await smartMigration();
    
    // Step 3: Verify
    const success = await verifyMigration();
    
    if (success) {
      log('green', '\n🎉 Smart migration completed successfully!');
      log('blue', `📦 Backup: ${backupFile}`);
      log('cyan', '\n✅ All tables now use unified format:');
      log('yellow', '   - DOC{YYYYMMDD}{XXX} for doctors');
      log('yellow', '   - PAT{YYYYMMDD}{XXX} for patients');
      log('yellow', '   - APT{YYYYMMDD}{XXX} for appointments');
      log('yellow', '   - DEPT{YYYYMMDD}{XXX} for departments');
      log('yellow', '   - ROOM{YYYYMMDD}{XXX} for rooms');
      log('yellow', '   - MED{YYYYMMDD}{XXX} for medical records');
    } else {
      log('yellow', '\n⚠️  Migration completed with some issues. Check details above.');
    }
    
  } catch (error) {
    log('red', `❌ Migration failed: ${error.message}`);
    log('yellow', 'Please restore from backup if needed.');
    process.exit(1);
  }
}

main();
