# 🇻🇳 Hospital Management System - Vietnam Focused Format Standards

## 📋 **OVERVIEW**

Đ<PERSON><PERSON> là bộ chuẩn format tối ưu cho thị trường Việt Nam, tuân thủ các quy định và thói quen địa phương.

## 🔑 **1. PRIMARY KEY STANDARDS - SIMPLIFIED**

### **Format Pattern**: `{PREFIX}{SEQUENCE}`
- **PREFIX**: 3-4 ký tự viết hoa
- **SEQUENCE**: <PERSON><PERSON> thứ tự tăng dần (6 chữ số)
- **Bỏ phần năm tạo** để đơn giản hóa

### **Examples**:
```
DOC000001  // Doctor #1
PAT000001  // Patient #1
APT000001  // Appointment #1
```

### **Table-Specific Prefixes**:
| Table | Prefix | Example | Description |
|-------|--------|---------|-------------|
| **doctors** | `DOC` | `DOC000001` | B<PERSON><PERSON> s<PERSON> |
| **patients** | `BN` | `BN000001` | Bệnh nhân |
| **appointments** | `LK` | `LK000001` | Lịch khám |
| **departments** | `KHOA` | `KHOA001` | Khoa |
| **rooms** | `PHONG` | `PHONG001` | Phòng |
| **medical_records** | `HSBA` | `HSBA000001` | Hồ sơ bệnh án |
| **prescriptions** | `TOA` | `TOA000001` | Toa thuốc |
| **billing** | `HD` | `HD000001` | Hóa đơn |

## 🏥 **2. VIETNAM MEDICAL STANDARDS**

### **License Numbers**: `VN-{TYPE}-{4DIGITS}`
```
VN-BS-1234    // Bác sĩ
VN-DD-1234    // Dược sĩ  
VN-YT-1234    // Y tá
VN-KT-1234    // Kỹ thuật viên
VN-XN-1234    // Xét nghiệm
VN-CDHA-1234  // Chẩn đoán hình ảnh (ngoại lệ 4 chữ cái)
```

### **Medical Specialties (Vietnamese)**:
```
Tim mạch, Thần kinh, Nhi khoa, Sản phụ khoa, 
Ngoại khoa, Nội khoa, Mắt, Tai mũi họng,
Da liễu, Tâm thần, Ung bướu, Gây mê hồi sức
```

### **Room Types (Vietnamese)**:
```
Phòng khám, Phòng mổ, Phòng cấp cứu, 
Phòng bệnh, Phòng hồi sức, Phòng xét nghiệm,
Phòng chẩn đoán hình ảnh, Phòng vật lý trị liệu
```

## 📞 **3. VIETNAM CONTACT STANDARDS**

### **Phone Numbers**: 10 digits starting with 0
```
VALID:
**********   // Viettel
**********   // Vinaphone  
**********   // Mobifone
**********   // Landline HCMC
**********   // Landline Hanoi

INVALID:
84901234567  // Missing leading 0
901234567    // Too short
**********8  // Too long
**********   // Wrong starting digit
```

### **Email Addresses**: Standard format with Vietnam domains
```
PREFERRED:
<EMAIL>
<EMAIL>
<EMAIL>

ACCEPTABLE:
<EMAIL>
<EMAIL>
<EMAIL>
```

### **Address Format (Vietnam Specific)**:
```json
{
  "so_nha": "123",
  "duong": "Nguyễn Văn Cừ",
  "phuong_xa": "Phường 4",
  "quan_huyen": "Quận 5",
  "tinh_tp": "TP. Hồ Chí Minh",
  "ma_buu_dien": "70000"
}
```

## 🩺 **4. VIETNAM MEDICAL DATA STANDARDS**

### **Blood Types**: Standard ABO + Rh
```
A+, A-, B+, B-, AB+, AB-, O+, O-
```

### **Vital Signs**: Metric units (Vietnam standard)
```json
{
  "nhiet_do": 36.5,           // °C
  "huyet_ap": "120/80",       // mmHg
  "mach": 72,                 // lần/phút
  "nhip_tho": 16,             // lần/phút
  "spo2": 98,                 // %
  "can_nang": 70.5,           // kg
  "chieu_cao": 175.0,         // cm
  "bmi": 23.0                 // kg/m²
}
```

### **Insurance Types (Vietnam)**:
```
BHYT     // Bảo hiểm y tế
BHXH     // Bảo hiểm xã hội
BHTN     // Bảo hiểm tự nguyện
TRA_TIEN // Trả tiền mặt
```

## 💰 **5. VIETNAM FINANCIAL STANDARDS**

### **Currency**: Vietnamese Dong (VND) only
```json
{
  "so_tien": 500000,          // Always in VND, no decimals
  "don_vi": "VND",
  "dinh_dang": "500,000 ₫"
}
```

### **Payment Methods**:
```
TIEN_MAT     // Tiền mặt
CHUYEN_KHOAN // Chuyển khoản
THE_ATM      // Thẻ ATM
THE_VISA     // Thẻ Visa/Master
MOMO         // Ví MoMo
ZALOPAY      // ZaloPay
VNPAY        // VNPay
```

## ⏰ **6. VIETNAM DATE & TIME STANDARDS**

### **Date Format**: DD/MM/YYYY (Vietnam standard)
```
18/01/2025              // Date display
2025-01-18              // Database storage (ISO)
```

### **Time Format**: 24-hour
```
07:00-16:00    // Giờ làm việc
14:30-15:00    // Khung giờ khám
```

### **Working Schedule**:
```json
{
  "thu_hai": "07:00-16:00",
  "thu_ba": "07:00-16:00", 
  "thu_tu": "07:00-16:00",
  "thu_nam": "07:00-16:00",
  "thu_sau": "07:00-16:00",
  "thu_bay": "08:00-12:00",
  "chu_nhat": "nghi"
}
```

## 🏷️ **7. VIETNAM STATUS & ENUM VALUES**

### **Universal Status (Vietnamese)**:
```
hoat_dong, tam_ngung, hoan_thanh, huy_bo, het_han
```

### **Medical-Specific Status**:
```
// Appointment Status
dat_lich, xac_nhan, dang_kham, hoan_thanh, huy_bo, vang_mat

// Prescription Status  
ke_don, da_cap, cap_mot_phan, huy_bo, het_han

// Patient Status
dang_dieu_tri, xuat_vien, chuyen_vien, tu_vong

// Doctor Status
dang_lam, nghi_phep, tam_nghi, nghi_huu
```

## 🔐 **8. UNIQUENESS CONSTRAINTS**

### **Critical Unique Fields**:
```sql
-- Doctors table
doctor_id       UNIQUE  -- DOC000001
email          UNIQUE  -- <EMAIL>  
license_number UNIQUE  -- VN-BS-1234

-- Patients table
patient_id     UNIQUE  -- BN000001
email          UNIQUE  -- <EMAIL>
cccd_number    UNIQUE  -- Căn cước công dân
bhyt_number    UNIQUE  -- Số thẻ BHYT

-- Appointments table
appointment_id UNIQUE  -- LK000001
```

## 📊 **9. VALIDATION RULES**

### **Phone Number Validation**:
```regex
^0[0-9]{9}$
// Must start with 0, followed by exactly 9 digits
```

### **License Number Validation**:
```regex
^VN-[A-Z]{2,4}-\d{4}$
// VN- + 2-4 letters + - + 4 digits
```

### **Email Validation**:
```regex
^[^\s@]+@[^\s@]+\.[^\s@]+$
// Standard email format
```

### **CCCD/CMND Validation**:
```regex
^[0-9]{9}$|^[0-9]{12}$
// 9 digits (old CMND) or 12 digits (new CCCD)
```

### **BHYT Number Validation**:
```regex
^[A-Z]{2}[0-9]{1}[0-9]{2}[0-9]{2}[0-9]{5}$
// Format: HS4020123456789
```

## 🎯 **10. ADDITIONAL VIETNAM-SPECIFIC IMPROVEMENTS**

### **A. Enhanced Patient Information**:
```json
{
  "patient_id": "BN000001",
  "ho_ten": "Nguyễn Văn An",
  "ngay_sinh": "1990-01-15",
  "gioi_tinh": "nam",
  "cccd_cmnd": "123456789012",
  "bhyt_number": "HS4020123456789",
  "dan_toc": "Kinh",
  "ton_giao": "Không",
  "nghe_nghiep": "Kỹ sư",
  "dia_chi_thuong_tru": {...},
  "dia_chi_tam_tru": {...},
  "nguoi_lien_he_khan_cap": {...}
}
```

### **B. Vietnamese Medical Terminology**:
```json
{
  "chan_doan": "Viêm phổi",
  "trieu_chung": "Ho, sốt, khó thở",
  "tien_su_benh": "Cao huyết áp",
  "di_ung": "Penicillin",
  "benh_ly_man_tinh": "Tiểu đường type 2"
}
```

### **C. Hospital Hierarchy (Vietnam)**:
```
Bệnh viện Trung ương    // Central Hospital
Bệnh viện Tỉnh          // Provincial Hospital  
Bệnh viện Quận/Huyện    // District Hospital
Trạm Y tế Xã/Phường     // Commune Health Station
Phòng khám Tư nhân      // Private Clinic
```

### **D. Medical Insurance Integration**:
```json
{
  "loai_bao_hiem": "BHYT",
  "ma_the": "HS4020123456789",
  "noi_dang_ky_kham": "Bệnh viện Chợ Rẫy",
  "han_su_dung": "2025-12-31",
  "muc_huong": "100%",
  "so_lan_kham_trong_thang": 2
}
```

## 🚀 **11. IMPLEMENTATION PRIORITY**

### **Phase 1 (Critical)**:
1. ✅ Simplify ID format (remove year)
2. ✅ Implement phone validation (10 digits, start with 0)
3. ✅ Standardize license format (VN-XX-1234)
4. ✅ Add uniqueness constraints

### **Phase 2 (Important)**:
1. 🔧 Vietnamese terminology integration
2. 🔧 BHYT/Insurance system
3. 🔧 Address standardization
4. 🔧 Payment method localization

### **Phase 3 (Enhancement)**:
1. 📋 Medical hierarchy support
2. 📋 Advanced reporting (Vietnamese)
3. 📋 Integration with Vietnam health systems

## 📝 **12. MIGRATION SCRIPT REQUIREMENTS**

### **ID Format Changes**:
```sql
-- Current: DOC20250530001 → Target: DOC000001
-- Current: PAT20250530001 → Target: BN000001
-- Current: APT20250530001 → Target: LK000001
```

### **Phone Number Fixes**:
```sql
-- Ensure all phone numbers are 10 digits starting with 0
-- Convert international format to local format
-- Validate against Vietnam mobile/landline patterns
```

### **License Number Updates**:
```sql
-- Current: VN-MD-174835 → Target: VN-BS-1234
-- Ensure 4-digit suffix
-- Validate against Vietnam medical license types
```

## 🎯 **SUMMARY OF IMPROVEMENTS**

### **Simplified & Vietnam-Focused**:
- ✅ **Shorter IDs** - Remove year for simplicity
- ✅ **Vietnamese prefixes** - BN, LK, KHOA, PHONG, etc.
- ✅ **Local phone format** - 10 digits starting with 0
- ✅ **Vietnam license format** - VN-XX-1234
- ✅ **Vietnamese terminology** - Medical terms in Vietnamese
- ✅ **Local payment methods** - MoMo, ZaloPay, VNPay
- ✅ **BHYT integration** - Vietnam health insurance
- ✅ **Address format** - Vietnam administrative divisions

### **Enhanced Uniqueness**:
- ✅ **Doctor**: ID + Email + License (3 unique constraints)
- ✅ **Patient**: ID + Email + CCCD + BHYT (4 unique constraints)
- ✅ **Appointment**: ID + unique scheduling rules

---

**Document Version**: 2.0 (Vietnam Focused)  
**Last Updated**: 2025-01-30  
**Target Market**: Vietnam Healthcare System  
**Compliance**: Vietnam Ministry of Health Standards
