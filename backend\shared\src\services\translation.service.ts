// ============================================================================
// TRANSLATION SERVICE - BILINGUAL VIETNAM SYSTEM
// ============================================================================

export interface TranslationMapping {
  [key: string]: string;
}

export interface FieldTranslation {
  field_key: string;
  vietnamese_label: string;
  english_label: string;
  description?: string;
}

// ============================================================================
// MEDICAL SPECIALTIES - ENGLISH TO VIETNAMESE
// ============================================================================

export const SPECIALTY_TRANSLATIONS: TranslationMapping = {
  'cardiology': 'Tim mạch',
  'neurology': 'Thần kinh',
  'pediatrics': 'Nhi khoa',
  'obstetrics_gynecology': 'Sản phụ khoa',
  'surgery': 'Ngoại khoa',
  'internal_medicine': 'Nội khoa',
  'ophthalmology': 'Mắt',
  'ent': 'Tai mũi họng',
  'dermatology': 'Da liễu',
  'psychiatry': 'Tâm thần',
  'oncology': 'Ung bướu',
  'anesthesiology': 'Gây mê hồi sức',
  'radiology': 'Chẩn đoán hình ảnh',
  'laboratory_medicine': 'Xét nghiệm',
  'emergency_medicine': 'Cấp cứu',
  'orthopedics': 'Chấn thương chỉnh hình',
  'urology': 'Tiết niệu',
  'gastroenterology': 'Tiêu hóa',
  'pulmonology': 'Hô hấp',
  'endocrinology': 'Nội tiết'
};

// ============================================================================
// STATUS TRANSLATIONS - ENGLISH TO VIETNAMESE
// ============================================================================

export const STATUS_TRANSLATIONS: TranslationMapping = {
  // Doctor Status
  'active': 'Đang làm việc',
  'inactive': 'Tạm nghỉ',
  'on_leave': 'Nghỉ phép',
  'suspended': 'Tạm đình chỉ',
  'retired': 'Nghỉ hưu',
  
  // Patient Status
  'discharged': 'Xuất viện',
  'transferred': 'Chuyển viện',
  'deceased': 'Tử vong',
  
  // Appointment Status
  'scheduled': 'Đã đặt lịch',
  'confirmed': 'Đã xác nhận',
  'in_progress': 'Đang khám',
  'completed': 'Hoàn thành',
  'cancelled': 'Đã hủy',
  'no_show': 'Vắng mặt',
  'rescheduled': 'Đã dời lịch',
  
  // Room Status
  'available': 'Sẵn sàng',
  'occupied': 'Đang sử dụng',
  'maintenance': 'Bảo trì',
  'out_of_service': 'Ngừng hoạt động',
  
  // General Status
  'pending': 'Đang chờ',
  'approved': 'Đã duyệt',
  'rejected': 'Từ chối',
  'expired': 'Hết hạn'
};

// ============================================================================
// ROOM TYPE TRANSLATIONS - ENGLISH TO VIETNAMESE
// ============================================================================

export const ROOM_TYPE_TRANSLATIONS: TranslationMapping = {
  'consultation': 'Phòng khám',
  'surgery': 'Phòng mổ',
  'emergency': 'Phòng cấp cứu',
  'ward': 'Phòng bệnh',
  'icu': 'Phòng hồi sức',
  'laboratory': 'Phòng xét nghiệm',
  'radiology': 'Phòng chẩn đoán hình ảnh',
  'pharmacy': 'Phòng thuốc',
  'rehabilitation': 'Phòng phục hồi chức năng',
  'operating_room': 'Phòng phẫu thuật',
  'recovery': 'Phòng hồi phục',
  'isolation': 'Phòng cách ly'
};

// ============================================================================
// APPOINTMENT TYPE TRANSLATIONS - ENGLISH TO VIETNAMESE
// ============================================================================

export const APPOINTMENT_TYPE_TRANSLATIONS: TranslationMapping = {
  'consultation': 'Khám bệnh',
  'follow_up': 'Tái khám',
  'emergency': 'Cấp cứu',
  'surgery': 'Phẫu thuật',
  'checkup': 'Kiểm tra sức khỏe',
  'vaccination': 'Tiêm chủng',
  'screening': 'Tầm soát',
  'therapy': 'Điều trị',
  'rehabilitation': 'Phục hồi chức năng'
};

// ============================================================================
// MEDICAL TERMS TRANSLATIONS - ENGLISH TO VIETNAMESE
// ============================================================================

export const MEDICAL_TRANSLATIONS: TranslationMapping = {
  // Diagnosis
  'hypertension': 'Cao huyết áp',
  'diabetes_type_1': 'Tiểu đường type 1',
  'diabetes_type_2': 'Tiểu đường type 2',
  'pneumonia': 'Viêm phổi',
  'covid_19': 'COVID-19',
  'influenza': 'Cúm',
  'bronchitis': 'Viêm phế quản',
  'asthma': 'Hen suyễn',
  'heart_disease': 'Bệnh tim',
  'stroke': 'Đột quỵ',
  'cancer': 'Ung thư',
  'arthritis': 'Viêm khớp',
  'osteoporosis': 'Loãng xương',
  
  // Symptoms
  'fever': 'Sốt',
  'cough': 'Ho',
  'shortness_of_breath': 'Khó thở',
  'chest_pain': 'Đau ngực',
  'headache': 'Đau đầu',
  'nausea': 'Buồn nôn',
  'vomiting': 'Nôn',
  'diarrhea': 'Tiêu chảy',
  'fatigue': 'Mệt mỏi',
  'dizziness': 'Chóng mặt',
  'abdominal_pain': 'Đau bụng',
  'back_pain': 'Đau lưng',
  
  // Medications
  'paracetamol': 'Paracetamol (hạ sốt, giảm đau)',
  'ibuprofen': 'Ibuprofen (chống viêm)',
  'amoxicillin': 'Amoxicillin (kháng sinh)',
  'amlodipine': 'Amlodipine (hạ huyết áp)',
  'metformin': 'Metformin (tiểu đường)',
  'aspirin': 'Aspirin (chống đông máu)',
  'omeprazole': 'Omeprazole (dạ dày)',
  'simvastatin': 'Simvastatin (hạ cholesterol)'
};

// ============================================================================
// PAYMENT METHOD TRANSLATIONS - ENGLISH TO VIETNAMESE
// ============================================================================

export const PAYMENT_TRANSLATIONS: TranslationMapping = {
  'cash': 'Tiền mặt',
  'bank_transfer': 'Chuyển khoản',
  'credit_card': 'Thẻ tín dụng',
  'debit_card': 'Thẻ ghi nợ',
  'momo': 'Ví MoMo',
  'zalopay': 'ZaloPay',
  'vnpay': 'VNPay',
  'insurance': 'Bảo hiểm y tế',
  'bhyt': 'BHYT',
  'bhxh': 'BHXH',
  'company_insurance': 'Bảo hiểm công ty'
};

// ============================================================================
// FIELD LABEL TRANSLATIONS - ENGLISH TO VIETNAMESE
// ============================================================================

export const FIELD_TRANSLATIONS: TranslationMapping = {
  'doctor_id': 'Mã bác sĩ',
  'patient_id': 'Mã bệnh nhân',
  'appointment_id': 'Mã lịch khám',
  'department_id': 'Mã khoa',
  'room_id': 'Mã phòng',
  'full_name': 'Họ và tên',
  'first_name': 'Tên',
  'last_name': 'Họ',
  'email': 'Email',
  'phone_number': 'Số điện thoại',
  'license_number': 'Số bằng cấp',
  'specialty': 'Chuyên khoa',
  'experience_years': 'Số năm kinh nghiệm',
  'consultation_fee': 'Phí khám bệnh',
  'status': 'Trạng thái',
  'gender': 'Giới tính',
  'date_of_birth': 'Ngày sinh',
  'blood_type': 'Nhóm máu',
  'address': 'Địa chỉ',
  'emergency_contact': 'Liên hệ khẩn cấp',
  'insurance_number': 'Số bảo hiểm',
  'appointment_date': 'Ngày khám',
  'appointment_time': 'Giờ khám',
  'duration': 'Thời lượng',
  'room_type': 'Loại phòng',
  'room_number': 'Số phòng',
  'capacity': 'Sức chứa',
  'equipment': 'Thiết bị',
  'notes': 'Ghi chú',
  'diagnosis': 'Chẩn đoán',
  'symptoms': 'Triệu chứng',
  'medications': 'Thuốc',
  'allergies': 'Dị ứng',
  'medical_history': 'Tiền sử bệnh',
  'vital_signs': 'Dấu hiệu sinh tồn',
  'temperature': 'Nhiệt độ',
  'blood_pressure': 'Huyết áp',
  'heart_rate': 'Nhịp tim',
  'weight': 'Cân nặng',
  'height': 'Chiều cao',
  'bmi': 'Chỉ số BMI',
  'created_at': 'Ngày tạo',
  'updated_at': 'Ngày cập nhật'
};

// ============================================================================
// TRANSLATION SERVICE CLASS
// ============================================================================

export class TranslationService {
  
  /**
   * Translate medical specialty from English to Vietnamese
   */
  translateSpecialty(englishSpecialty: string): string {
    return SPECIALTY_TRANSLATIONS[englishSpecialty] || englishSpecialty;
  }
  
  /**
   * Translate status from English to Vietnamese
   */
  translateStatus(englishStatus: string): string {
    return STATUS_TRANSLATIONS[englishStatus] || englishStatus;
  }
  
  /**
   * Translate room type from English to Vietnamese
   */
  translateRoomType(englishRoomType: string): string {
    return ROOM_TYPE_TRANSLATIONS[englishRoomType] || englishRoomType;
  }
  
  /**
   * Translate appointment type from English to Vietnamese
   */
  translateAppointmentType(englishType: string): string {
    return APPOINTMENT_TYPE_TRANSLATIONS[englishType] || englishType;
  }
  
  /**
   * Translate medical term from English to Vietnamese
   */
  translateMedicalTerm(englishTerm: string): string {
    return MEDICAL_TRANSLATIONS[englishTerm] || englishTerm;
  }
  
  /**
   * Translate payment method from English to Vietnamese
   */
  translatePaymentMethod(englishMethod: string): string {
    return PAYMENT_TRANSLATIONS[englishMethod] || englishMethod;
  }
  
  /**
   * Get field label in Vietnamese
   */
  getFieldLabel(fieldKey: string): string {
    return FIELD_TRANSLATIONS[fieldKey] || fieldKey;
  }
  
  /**
   * Format date for Vietnamese display
   */
  formatVietnameseDate(isoDate: string): string {
    const date = new Date(isoDate);
    return date.toLocaleDateString('vi-VN', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  }
  
  /**
   * Format datetime for Vietnamese display
   */
  formatVietnameseDateTime(isoDate: string): string {
    const date = new Date(isoDate);
    return date.toLocaleString('vi-VN', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }
  
  /**
   * Format currency for Vietnamese display
   */
  formatVietnameseCurrency(amount: number): string {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND'
    }).format(amount);
  }
  
  /**
   * Transform data object for Vietnamese UI display
   */
  formatForDisplay(data: any): any {
    const displayData = { ...data };
    
    // Add Vietnamese translations for common fields
    if (data.specialty) {
      displayData.specialty_display = this.translateSpecialty(data.specialty);
    }
    
    if (data.status) {
      displayData.status_display = this.translateStatus(data.status);
    }
    
    if (data.room_type) {
      displayData.room_type_display = this.translateRoomType(data.room_type);
    }
    
    if (data.type) {
      displayData.type_display = this.translateAppointmentType(data.type);
    }
    
    if (data.created_at) {
      displayData.created_at_display = this.formatVietnameseDate(data.created_at);
    }
    
    if (data.updated_at) {
      displayData.updated_at_display = this.formatVietnameseDate(data.updated_at);
    }
    
    if (data.consultation_fee) {
      displayData.consultation_fee_display = this.formatVietnameseCurrency(data.consultation_fee);
    }
    
    return displayData;
  }
  
  /**
   * Get dropdown options with Vietnamese labels
   */
  getSpecialtyOptions(): Array<{value: string, label: string}> {
    return Object.entries(SPECIALTY_TRANSLATIONS).map(([value, label]) => ({
      value,
      label
    }));
  }
  
  getStatusOptions(type: 'doctor' | 'patient' | 'appointment' | 'room' = 'doctor'): Array<{value: string, label: string}> {
    const statusKeys = {
      doctor: ['active', 'inactive', 'on_leave', 'suspended', 'retired'],
      patient: ['active', 'discharged', 'transferred', 'deceased'],
      appointment: ['scheduled', 'confirmed', 'in_progress', 'completed', 'cancelled', 'no_show'],
      room: ['available', 'occupied', 'maintenance', 'out_of_service']
    };
    
    return statusKeys[type].map(value => ({
      value,
      label: STATUS_TRANSLATIONS[value] || value
    }));
  }
  
  getRoomTypeOptions(): Array<{value: string, label: string}> {
    return Object.entries(ROOM_TYPE_TRANSLATIONS).map(([value, label]) => ({
      value,
      label
    }));
  }
  
  getPaymentMethodOptions(): Array<{value: string, label: string}> {
    return Object.entries(PAYMENT_TRANSLATIONS).map(([value, label]) => ({
      value,
      label
    }));
  }
}

// ============================================================================
// SINGLETON INSTANCE
// ============================================================================

export const translationService = new TranslationService();

export default translationService;
