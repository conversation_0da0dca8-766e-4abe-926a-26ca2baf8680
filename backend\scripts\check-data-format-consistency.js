const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

// Initialize Supabase client
const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

// Format validation patterns
const PATTERNS = {
  DOCTOR_ID: /^DOC\d{6}$/,
  PATIENT_ID: /^PAT\d+$/,
  APPOINTMENT_ID: /^APT\d+$/,
  DEPARTMENT_ID: /^DEPT\d+$/,
  ROOM_ID: /^ROOM\d+$/,
  MEDICAL_RECORD_ID: /^MR\d+$/,
  PRESCRIPTION_ID: /^PRE\d+$/,
  UUID: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i,
  EMAIL: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  PHONE: /^[0-9+\-\s()]+$/,
  LICENSE_NUMBER: /^[A-Z]{2,4}\d{6,10}$/,
  BLOOD_TYPE: /^(A|B|AB|O)[+-]$/,
  TIME_RANGE: /^([01]?[0-9]|2[0-3]):[0-5][0-9]-([01]?[0-9]|2[0-3]):[0-5][0-9]$/,
  BLOOD_PRESSURE: /^\d{2,3}\/\d{2,3}$/
};

const ENUMS = {
  ROLE: ['admin', 'doctor', 'patient'],
  GENDER: ['male', 'female', 'other'],
  DOCTOR_STATUS: ['active', 'inactive', 'on_leave'],
  PATIENT_STATUS: ['active', 'inactive', 'deceased'],
  BLOOD_TYPE: ['A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-'],
  APPOINTMENT_TYPE: ['consultation', 'follow_up', 'emergency', 'surgery'],
  APPOINTMENT_STATUS: ['scheduled', 'confirmed', 'in_progress', 'completed', 'cancelled', 'no_show'],
  ROOM_TYPE: ['consultation', 'surgery', 'emergency', 'ward', 'icu', 'laboratory', 'Phòng khám', 'Phòng mổ', 'Phòng bệnh', 'Phòng hồi sức'],
  ROOM_STATUS: ['available', 'occupied', 'maintenance', 'out_of_service'],
  MEDICAL_RECORD_STATUS: ['active', 'archived'],
  PRESCRIPTION_STATUS: ['active', 'dispensed', 'expired', 'cancelled']
};

const RANGES = {
  EXPERIENCE_YEARS: { min: 0, max: 50 },
  CONSULTATION_FEE: { min: 0, max: 999999.99 },
  DURATION_MINUTES: { min: 15, max: 480 },
  ROOM_CAPACITY: { min: 1, max: 100 },
  TEMPERATURE: { min: 30.0, max: 45.0 },
  HEART_RATE: { min: 30, max: 200 },
  BLOOD_PRESSURE_SYSTOLIC: { min: 60, max: 250 },
  BLOOD_PRESSURE_DIASTOLIC: { min: 40, max: 150 },
  RESPIRATORY_RATE: { min: 8, max: 40 },
  OXYGEN_SATURATION: { min: 70.0, max: 100.0 },
  WEIGHT: { min: 0.5, max: 300.0 },
  HEIGHT: { min: 30.0, max: 250.0 },
  BMI: { min: 10.0, max: 50.0 }
};

function log(color, message) {
  const colors = {
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m',
    reset: '\x1b[0m'
  };
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function validateField(value, fieldName, validations) {
  const issues = [];

  if (value === null || value === undefined) {
    if (validations.required) {
      issues.push(`${fieldName}: Required field is null/undefined`);
    }
    return issues;
  }

  // Pattern validation
  if (validations.pattern && !validations.pattern.test(value)) {
    issues.push(`${fieldName}: Invalid format - "${value}" doesn't match pattern ${validations.pattern}`);
  }

  // Enum validation
  if (validations.enum && !validations.enum.includes(value)) {
    issues.push(`${fieldName}: Invalid enum value - "${value}" not in [${validations.enum.join(', ')}]`);
  }

  // Range validation
  if (validations.range && typeof value === 'number') {
    if (value < validations.range.min || value > validations.range.max) {
      issues.push(`${fieldName}: Out of range - ${value} not in [${validations.range.min}, ${validations.range.max}]`);
    }
  }

  // Length validation
  if (validations.maxLength && typeof value === 'string' && value.length > validations.maxLength) {
    issues.push(`${fieldName}: Too long - ${value.length} chars > ${validations.maxLength} max`);
  }

  return issues;
}

function validateWorkingHours(workingHours, recordId) {
  const issues = [];
  if (!workingHours || typeof workingHours !== 'object') return issues;

  const validDays = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];

  for (const [day, timeRange] of Object.entries(workingHours)) {
    if (!validDays.includes(day)) {
      issues.push(`${recordId} working_hours: Invalid day "${day}"`);
    }
    if (timeRange !== 'off' && typeof timeRange === 'string' && !PATTERNS.TIME_RANGE.test(timeRange)) {
      issues.push(`${recordId} working_hours: Invalid time format "${timeRange}" for ${day}`);
    }
  }

  return issues;
}

function validateVitalSigns(vitalSigns, recordId) {
  const issues = [];
  if (!vitalSigns || typeof vitalSigns !== 'object') return issues;

  if (vitalSigns.temperature && (vitalSigns.temperature < RANGES.TEMPERATURE.min || vitalSigns.temperature > RANGES.TEMPERATURE.max)) {
    issues.push(`${recordId} vital_signs.temperature: ${vitalSigns.temperature} out of range [${RANGES.TEMPERATURE.min}, ${RANGES.TEMPERATURE.max}]`);
  }

  if (vitalSigns.heart_rate && (vitalSigns.heart_rate < RANGES.HEART_RATE.min || vitalSigns.heart_rate > RANGES.HEART_RATE.max)) {
    issues.push(`${recordId} vital_signs.heart_rate: ${vitalSigns.heart_rate} out of range [${RANGES.HEART_RATE.min}, ${RANGES.HEART_RATE.max}]`);
  }

  if (vitalSigns.blood_pressure && !PATTERNS.BLOOD_PRESSURE.test(vitalSigns.blood_pressure)) {
    issues.push(`${recordId} vital_signs.blood_pressure: Invalid format "${vitalSigns.blood_pressure}"`);
  }

  return issues;
}

async function checkTableConsistency(tableName, validations) {
  log('blue', `\n🔍 Checking ${tableName} table...`);

  try {
    const { data, error } = await supabase
      .from(tableName)
      .select('*');

    if (error) {
      log('red', `❌ Error reading ${tableName}: ${error.message}`);
      return [];
    }

    if (!data || data.length === 0) {
      log('yellow', `⚠️  ${tableName} table is empty`);
      return [];
    }

    const allIssues = [];
    let recordCount = 0;
    let issueCount = 0;

    for (const record of data) {
      recordCount++;
      const recordIssues = [];

      // Validate each field
      for (const [fieldName, validation] of Object.entries(validations)) {
        const fieldIssues = validateField(record[fieldName], fieldName, validation);
        recordIssues.push(...fieldIssues);
      }

      // Special validations for complex fields
      if (tableName === 'doctors' && record.working_hours) {
        recordIssues.push(...validateWorkingHours(record.working_hours, record.doctor_id));
      }

      if (tableName === 'medical_records' && record.vital_signs) {
        recordIssues.push(...validateVitalSigns(record.vital_signs, record.record_id));
      }

      if (recordIssues.length > 0) {
        issueCount++;
        allIssues.push({
          table: tableName,
          recordId: record[Object.keys(record)[0]], // First field as ID
          issues: recordIssues
        });
      }
    }

    if (allIssues.length === 0) {
      log('green', `✅ ${tableName}: All ${recordCount} records are format-compliant`);
    } else {
      log('red', `❌ ${tableName}: Found ${allIssues.length} records with format issues out of ${recordCount} total`);

      // Show first few issues as examples
      allIssues.slice(0, 3).forEach(issue => {
        log('yellow', `   Record ${issue.recordId}:`);
        issue.issues.forEach(iss => log('yellow', `     - ${iss}`));
      });

      if (allIssues.length > 3) {
        log('yellow', `   ... and ${allIssues.length - 3} more records with issues`);
      }
    }

    return allIssues;

  } catch (error) {
    log('red', `❌ Error checking ${tableName}: ${error.message}`);
    return [];
  }
}

async function main() {
  log('cyan', '🔍 Hospital Management System - Data Format Consistency Check');
  log('cyan', '================================================================');

  const tableValidations = {
    doctors: {
      doctor_id: { pattern: PATTERNS.DOCTOR_ID, required: true },
      profile_id: { pattern: PATTERNS.UUID, required: true },
      license_number: { pattern: PATTERNS.LICENSE_NUMBER, required: true },
      experience_years: { range: RANGES.EXPERIENCE_YEARS },
      consultation_fee: { range: RANGES.CONSULTATION_FEE },
      department_id: { pattern: PATTERNS.DEPARTMENT_ID, required: true },
      status: { enum: ENUMS.DOCTOR_STATUS, required: true }
    },

    patients: {
      patient_id: { pattern: PATTERNS.PATIENT_ID, required: true },
      profile_id: { pattern: PATTERNS.UUID, required: true },
      gender: { enum: ENUMS.GENDER, required: true },
      blood_type: { enum: ENUMS.BLOOD_TYPE },
      status: { enum: ENUMS.PATIENT_STATUS, required: true }
    },

    appointments: {
      appointment_id: { pattern: PATTERNS.APPOINTMENT_ID, required: true },
      patient_id: { pattern: PATTERNS.PATIENT_ID, required: true },
      doctor_id: { pattern: PATTERNS.DOCTOR_ID, required: true },
      duration_minutes: { range: RANGES.DURATION_MINUTES, required: true },
      type: { enum: ENUMS.APPOINTMENT_TYPE, required: true },
      status: { enum: ENUMS.APPOINTMENT_STATUS, required: true },
      room_id: { pattern: PATTERNS.ROOM_ID }
    },

    departments: {
      department_id: { pattern: PATTERNS.DEPARTMENT_ID, required: true },
      name: { required: true, maxLength: 100 },
      head_doctor_id: { pattern: PATTERNS.DOCTOR_ID },
      phone_number: { pattern: PATTERNS.PHONE },
      email: { pattern: PATTERNS.EMAIL }
    },

    rooms: {
      room_id: { pattern: PATTERNS.ROOM_ID, required: true },
      room_number: { required: true, maxLength: 20 },
      room_type: { enum: ENUMS.ROOM_TYPE, required: true },
      department_id: { pattern: PATTERNS.DEPARTMENT_ID, required: true },
      capacity: { range: RANGES.ROOM_CAPACITY, required: true },
      status: { enum: ENUMS.ROOM_STATUS, required: true }
    },

    medical_records: {
      record_id: { pattern: PATTERNS.MEDICAL_RECORD_ID, required: true },
      patient_id: { pattern: PATTERNS.PATIENT_ID, required: true },
      doctor_id: { pattern: PATTERNS.DOCTOR_ID, required: true },
      appointment_id: { pattern: PATTERNS.APPOINTMENT_ID },
      status: { enum: ENUMS.MEDICAL_RECORD_STATUS, required: true }
    }
  };

  const allIssues = [];

  for (const [tableName, validations] of Object.entries(tableValidations)) {
    const tableIssues = await checkTableConsistency(tableName, validations);
    allIssues.push(...tableIssues);
  }

  // Summary
  log('cyan', '\n📊 SUMMARY REPORT');
  log('cyan', '==================');

  if (allIssues.length === 0) {
    log('green', '🎉 All tables are format-compliant! No inconsistencies found.');
  } else {
    log('red', `❌ Found ${allIssues.length} records with format inconsistencies across tables:`);

    // Group by table
    const issuesByTable = {};
    allIssues.forEach(issue => {
      if (!issuesByTable[issue.table]) {
        issuesByTable[issue.table] = [];
      }
      issuesByTable[issue.table].push(issue);
    });

    for (const [table, issues] of Object.entries(issuesByTable)) {
      log('yellow', `\n📋 ${table.toUpperCase()}: ${issues.length} records with issues`);

      // Count issue types
      const issueTypes = {};
      issues.forEach(issue => {
        issue.issues.forEach(iss => {
          const type = iss.split(':')[0];
          issueTypes[type] = (issueTypes[type] || 0) + 1;
        });
      });

      for (const [type, count] of Object.entries(issueTypes)) {
        log('yellow', `   - ${type}: ${count} occurrences`);
      }
    }

    log('cyan', '\n🔧 RECOMMENDED ACTIONS:');
    log('cyan', '1. Review and fix format inconsistencies');
    log('cyan', '2. Update validation rules in services');
    log('cyan', '3. Run data migration scripts if needed');
    log('cyan', '4. Implement stricter input validation');
  }
}

main().catch(console.error);
