const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

function log(color, message) {
  const colors = {
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m',
    reset: '\x1b[0m'
  };
  console.log(`${colors[color]}${message}${colors.reset}`);
}

async function createBackup() {
  log('cyan', '📦 Creating backup of current data...');
  
  try {
    const { data: doctors } = await supabase.from('doctors').select('*');
    const { data: appointments } = await supabase.from('appointments').select('*');
    const { data: medicalRecords } = await supabase.from('medical_records').select('*');
    
    const backup = {
      timestamp: new Date().toISOString(),
      doctors,
      appointments,
      medical_records: medicalRecords
    };
    
    const fs = require('fs');
    const backupFile = `backup-${Date.now()}.json`;
    fs.writeFileSync(backupFile, JSON.stringify(backup, null, 2));
    
    log('green', `✅ Backup created: ${backupFile}`);
    return backupFile;
  } catch (error) {
    log('red', `❌ Backup failed: ${error.message}`);
    throw error;
  }
}

async function fixDoctorIds() {
  log('cyan', '\n🔧 Step 1: Fixing Doctor IDs...');
  
  try {
    const { data: doctors } = await supabase.from('doctors').select('*');
    
    // Create mapping of old ID to new ID
    const idMapping = {};
    const updates = [];
    
    doctors.forEach((doctor, index) => {
      if (!/^DOC\d{6}$/.test(doctor.doctor_id)) {
        const newId = `DOC${String(index + 1).padStart(6, '0')}`;
        idMapping[doctor.doctor_id] = newId;
        updates.push({
          oldId: doctor.doctor_id,
          newId: newId,
          profile_id: doctor.profile_id
        });
      }
    });
    
    log('blue', `Found ${updates.length} doctor IDs to fix:`);
    
    for (const update of updates) {
      log('yellow', `   ${update.oldId} → ${update.newId}`);
      
      const { error } = await supabase
        .from('doctors')
        .update({ doctor_id: update.newId })
        .eq('doctor_id', update.oldId);
        
      if (error) {
        log('red', `❌ Failed to update ${update.oldId}: ${error.message}`);
      } else {
        log('green', `✅ Updated ${update.oldId} → ${update.newId}`);
      }
    }
    
    return idMapping;
    
  } catch (error) {
    log('red', `❌ Doctor ID fix failed: ${error.message}`);
    throw error;
  }
}

async function updateForeignKeys(idMapping) {
  log('cyan', '\n🔧 Step 2: Updating Foreign Key References...');
  
  try {
    // Update appointments
    log('blue', 'Updating appointments...');
    const { data: appointments } = await supabase.from('appointments').select('*');
    
    for (const appointment of appointments) {
      if (idMapping[appointment.doctor_id]) {
        const newDoctorId = idMapping[appointment.doctor_id];
        
        const { error } = await supabase
          .from('appointments')
          .update({ doctor_id: newDoctorId })
          .eq('appointment_id', appointment.appointment_id);
          
        if (error) {
          log('red', `❌ Failed to update appointment ${appointment.appointment_id}: ${error.message}`);
        } else {
          log('green', `✅ Updated appointment ${appointment.appointment_id}: ${appointment.doctor_id} → ${newDoctorId}`);
        }
      }
    }
    
    // Update medical records
    log('blue', 'Updating medical records...');
    const { data: medicalRecords } = await supabase.from('medical_records').select('*');
    
    for (const record of medicalRecords) {
      if (idMapping[record.doctor_id]) {
        const newDoctorId = idMapping[record.doctor_id];
        
        const { error } = await supabase
          .from('medical_records')
          .update({ doctor_id: newDoctorId })
          .eq('record_id', record.record_id);
          
        if (error) {
          log('red', `❌ Failed to update medical record ${record.record_id}: ${error.message}`);
        } else {
          log('green', `✅ Updated medical record ${record.record_id}: ${record.doctor_id} → ${newDoctorId}`);
        }
      }
    }
    
  } catch (error) {
    log('red', `❌ Foreign key update failed: ${error.message}`);
    throw error;
  }
}

async function fixLicenseNumbers() {
  log('cyan', '\n🔧 Step 3: Fixing License Numbers...');
  
  try {
    const { data: doctors } = await supabase.from('doctors').select('*');
    
    for (const doctor of doctors) {
      if (!/^[A-Z]{2,4}\d{6,10}$/.test(doctor.license_number)) {
        // Extract letters and numbers
        const letters = doctor.license_number.match(/[A-Z]+/g)?.join('') || 'MD';
        const numbers = doctor.license_number.match(/\d+/g)?.join('') || '000000';
        
        // Create standardized format
        const prefix = letters.substring(0, 4);
        const suffix = numbers.padStart(6, '0').substring(0, 10);
        const standardized = prefix + suffix;
        
        log('yellow', `   ${doctor.doctor_id}: "${doctor.license_number}" → "${standardized}"`);
        
        const { error } = await supabase
          .from('doctors')
          .update({ license_number: standardized })
          .eq('doctor_id', doctor.doctor_id);
          
        if (error) {
          log('red', `❌ Failed to update license for ${doctor.doctor_id}: ${error.message}`);
        } else {
          log('green', `✅ Updated license for ${doctor.doctor_id}`);
        }
      }
    }
    
  } catch (error) {
    log('red', `❌ License number fix failed: ${error.message}`);
    throw error;
  }
}

async function verifyFixes() {
  log('cyan', '\n🔍 Step 4: Verifying Fixes...');
  
  try {
    const { data: doctors } = await supabase.from('doctors').select('*');
    const { data: appointments } = await supabase.from('appointments').select('*');
    const { data: medicalRecords } = await supabase.from('medical_records').select('*');
    
    // Check doctor IDs
    const invalidDoctorIds = doctors.filter(d => !/^DOC\d{6}$/.test(d.doctor_id));
    const invalidLicenses = doctors.filter(d => !/^[A-Z]{2,4}\d{6,10}$/.test(d.license_number));
    
    // Check foreign key references
    const invalidAppointmentRefs = appointments.filter(a => {
      return !doctors.some(d => d.doctor_id === a.doctor_id);
    });
    
    const invalidMedicalRecordRefs = medicalRecords.filter(m => {
      return !doctors.some(d => d.doctor_id === m.doctor_id);
    });
    
    log('blue', '\n📊 Verification Results:');
    log(invalidDoctorIds.length === 0 ? 'green' : 'red', 
        `   Doctor IDs: ${invalidDoctorIds.length} invalid (${invalidDoctorIds.length === 0 ? 'PASS' : 'FAIL'})`);
    log(invalidLicenses.length === 0 ? 'green' : 'red', 
        `   License Numbers: ${invalidLicenses.length} invalid (${invalidLicenses.length === 0 ? 'PASS' : 'FAIL'})`);
    log(invalidAppointmentRefs.length === 0 ? 'green' : 'red', 
        `   Appointment References: ${invalidAppointmentRefs.length} broken (${invalidAppointmentRefs.length === 0 ? 'PASS' : 'FAIL'})`);
    log(invalidMedicalRecordRefs.length === 0 ? 'green' : 'red', 
        `   Medical Record References: ${invalidMedicalRecordRefs.length} broken (${invalidMedicalRecordRefs.length === 0 ? 'PASS' : 'FAIL'})`);
    
    if (invalidDoctorIds.length === 0 && invalidLicenses.length === 0 && 
        invalidAppointmentRefs.length === 0 && invalidMedicalRecordRefs.length === 0) {
      log('green', '\n🎉 All format issues have been successfully fixed!');
      return true;
    } else {
      log('red', '\n❌ Some issues remain. Please check the details above.');
      return false;
    }
    
  } catch (error) {
    log('red', `❌ Verification failed: ${error.message}`);
    throw error;
  }
}

async function main() {
  log('cyan', '🔧 Hospital Management System - Format Inconsistency Fix');
  log('cyan', '========================================================');
  
  try {
    // Step 0: Create backup
    const backupFile = await createBackup();
    
    // Step 1: Fix doctor IDs
    const idMapping = await fixDoctorIds();
    
    // Step 2: Update foreign keys
    await updateForeignKeys(idMapping);
    
    // Step 3: Fix license numbers
    await fixLicenseNumbers();
    
    // Step 4: Verify fixes
    const success = await verifyFixes();
    
    if (success) {
      log('green', '\n✅ Migration completed successfully!');
      log('blue', `📦 Backup saved as: ${backupFile}`);
      log('cyan', '\n🎯 Next Steps:');
      log('yellow', '1. Test application functionality');
      log('yellow', '2. Update validation rules in services');
      log('yellow', '3. Deploy updated code');
    } else {
      log('red', '\n❌ Migration completed with issues. Please review and fix manually.');
    }
    
  } catch (error) {
    log('red', `❌ Migration failed: ${error.message}`);
    log('yellow', 'Please restore from backup if needed.');
    process.exit(1);
  }
}

// Add confirmation prompt
const readline = require('readline');
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

log('yellow', '⚠️  This script will modify data in your Supabase database.');
log('yellow', '   A backup will be created before making changes.');
rl.question('Do you want to proceed? (yes/no): ', (answer) => {
  if (answer.toLowerCase() === 'yes' || answer.toLowerCase() === 'y') {
    rl.close();
    main();
  } else {
    log('blue', 'Operation cancelled.');
    rl.close();
  }
});
