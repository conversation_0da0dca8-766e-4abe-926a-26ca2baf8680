# 🇻🇳🇺🇸 Hospital Management System - Bilingual Vietnam Standards

## 📋 **OVERVIEW**

<PERSON><PERSON> thống song ngữ tối ưu cho thị trường Vi<PERSON>, sử dụng **tiếng <PERSON>h cho các trường kỹ thuật/y tế** và **tiếng Việt cho giao diện người dùng**.

## 🔑 **1. ID FORMATS - ENGLISH (Technical)**

### **Rationale**: IDs cần consistency, dễ integrate với hệ thống quốc tế
```
DOC000001  // Doctor (English - technical)
PAT000001  // Patient (English - technical) 
APT000001  // Appointment (English - technical)
DEPT000001 // Department (English - technical)
ROOM000001 // Room (English - technical)
MED000001  // Medical Record (English - technical)
PRESC000001// Prescription (English - technical)
BILL000001 // Billing (English - technical)
```

### **Display Names - Vietnamese (User Interface)**:
```
DOC000001 → "<PERSON><PERSON><PERSON> sĩ #000001"
PAT000001 → "Bệnh nhân #000001"
APT000001 → "Lịch khám #000001"
DEPT000001 → "Khoa #000001"
ROOM000001 → "Phòng #000001"
```

## 🏥 **2. MEDICAL DATA - ENGLISH (International Standards)**

### **Medical Specialties - English (Database)**:
```sql
-- Database storage (English for consistency)
specialties: [
  'cardiology', 'neurology', 'pediatrics', 'obstetrics_gynecology',
  'surgery', 'internal_medicine', 'ophthalmology', 'ent',
  'dermatology', 'psychiatry', 'oncology', 'anesthesiology',
  'radiology', 'laboratory_medicine', 'emergency_medicine'
]
```

### **Display Mapping - Vietnamese (UI)**:
```typescript
const SPECIALTY_DISPLAY = {
  'cardiology': 'Tim mạch',
  'neurology': 'Thần kinh', 
  'pediatrics': 'Nhi khoa',
  'obstetrics_gynecology': 'Sản phụ khoa',
  'surgery': 'Ngoại khoa',
  'internal_medicine': 'Nội khoa',
  'ophthalmology': 'Mắt',
  'ent': 'Tai mũi họng',
  'dermatology': 'Da liễu',
  'psychiatry': 'Tâm thần',
  'oncology': 'Ung bướu',
  'anesthesiology': 'Gây mê hồi sức',
  'radiology': 'Chẩn đoán hình ảnh',
  'laboratory_medicine': 'Xét nghiệm',
  'emergency_medicine': 'Cấp cứu'
};
```

### **Room Types - English (Database)**:
```sql
-- Database storage (English)
room_types: [
  'consultation', 'surgery', 'emergency', 'ward', 'icu', 
  'laboratory', 'radiology', 'pharmacy', 'rehabilitation'
]
```

### **Display Mapping - Vietnamese (UI)**:
```typescript
const ROOM_TYPE_DISPLAY = {
  'consultation': 'Phòng khám',
  'surgery': 'Phòng mổ',
  'emergency': 'Phòng cấp cứu',
  'ward': 'Phòng bệnh',
  'icu': 'Phòng hồi sức',
  'laboratory': 'Phòng xét nghiệm',
  'radiology': 'Phòng chẩn đoán hình ảnh',
  'pharmacy': 'Phòng thuốc',
  'rehabilitation': 'Phòng phục hồi chức năng'
};
```

## 📞 **3. CONTACT INFO - MIXED APPROACH**

### **Phone Numbers - Vietnam Format (Local)**:
```
**********  // Vietnam standard (10 digits, start with 0)
```

### **Email - English (International)**:
```
<EMAIL>    // English format
<EMAIL>       // English format
<EMAIL>           // Mixed (English structure, VN domain)
```

### **Address - Vietnamese (Local)**:
```json
{
  "so_nha": "123",
  "duong": "Nguyễn Văn Cừ",
  "phuong_xa": "Phường 4",
  "quan_huyen": "Quận 5",
  "tinh_tp": "TP. Hồ Chí Minh",
  "quoc_gia": "Vietnam"
}
```

## 🩺 **4. MEDICAL TERMINOLOGY - BILINGUAL**

### **Status Values - English (Database)**:
```sql
-- Doctor Status (English for API consistency)
doctor_status: ['active', 'inactive', 'on_leave', 'suspended', 'retired']

-- Patient Status (English for medical records)
patient_status: ['active', 'discharged', 'transferred', 'deceased']

-- Appointment Status (English for workflow)
appointment_status: ['scheduled', 'confirmed', 'in_progress', 'completed', 'cancelled', 'no_show']
```

### **Display Mapping - Vietnamese (UI)**:
```typescript
const STATUS_DISPLAY = {
  // Doctor Status
  'active': 'Đang làm việc',
  'inactive': 'Tạm nghỉ',
  'on_leave': 'Nghỉ phép',
  'suspended': 'Tạm đình chỉ',
  'retired': 'Nghỉ hưu',
  
  // Patient Status
  'active': 'Đang điều trị',
  'discharged': 'Xuất viện',
  'transferred': 'Chuyển viện',
  'deceased': 'Tử vong',
  
  // Appointment Status
  'scheduled': 'Đã đặt lịch',
  'confirmed': 'Đã xác nhận',
  'in_progress': 'Đang khám',
  'completed': 'Hoàn thành',
  'cancelled': 'Đã hủy',
  'no_show': 'Vắng mặt'
};
```

### **Medical Terms - English (Database)**:
```sql
-- Diagnosis (English for international compatibility)
diagnosis: 'hypertension', 'diabetes_type_2', 'pneumonia', 'covid_19'

-- Symptoms (English for medical accuracy)
symptoms: 'fever', 'cough', 'shortness_of_breath', 'chest_pain'

-- Medications (English for drug database compatibility)
medications: 'amlodipine', 'metformin', 'amoxicillin', 'paracetamol'
```

### **Display Mapping - Vietnamese (UI)**:
```typescript
const MEDICAL_DISPLAY = {
  // Diagnosis
  'hypertension': 'Cao huyết áp',
  'diabetes_type_2': 'Tiểu đường type 2',
  'pneumonia': 'Viêm phổi',
  'covid_19': 'COVID-19',
  
  // Symptoms
  'fever': 'Sốt',
  'cough': 'Ho',
  'shortness_of_breath': 'Khó thở',
  'chest_pain': 'Đau ngực',
  
  // Medications
  'amlodipine': 'Amlodipine (hạ huyết áp)',
  'metformin': 'Metformin (tiểu đường)',
  'amoxicillin': 'Amoxicillin (kháng sinh)',
  'paracetamol': 'Paracetamol (hạ sốt, giảm đau)'
};
```

## 💰 **5. FINANCIAL - MIXED APPROACH**

### **Currency - Vietnamese (Local)**:
```json
{
  "amount": 500000,
  "currency": "VND",
  "display": "500,000 ₫"
}
```

### **Payment Methods - English (Database) + Vietnamese (Display)**:
```typescript
// Database (English for integration)
const PAYMENT_METHODS = [
  'cash', 'bank_transfer', 'credit_card', 'debit_card',
  'momo', 'zalopay', 'vnpay', 'insurance'
];

// Display (Vietnamese)
const PAYMENT_DISPLAY = {
  'cash': 'Tiền mặt',
  'bank_transfer': 'Chuyển khoản',
  'credit_card': 'Thẻ tín dụng',
  'debit_card': 'Thẻ ghi nợ',
  'momo': 'Ví MoMo',
  'zalopay': 'ZaloPay',
  'vnpay': 'VNPay',
  'insurance': 'Bảo hiểm y tế'
};
```

## 🔐 **6. VALIDATION RULES - BILINGUAL**

### **Error Messages - Vietnamese (User-facing)**:
```typescript
const VALIDATION_MESSAGES_VN = {
  'required': 'Trường này là bắt buộc',
  'invalid_phone': 'Số điện thoại phải có 10 chữ số và bắt đầu bằng 0',
  'invalid_email': 'Email không đúng định dạng',
  'invalid_license': 'Số bằng cấp phải có định dạng VN-XX-1234',
  'duplicate_email': 'Email này đã được sử dụng',
  'duplicate_license': 'Số bằng cấp này đã tồn tại',
  'invalid_date': 'Ngày không hợp lệ',
  'future_date': 'Ngày không được trong tương lai'
};
```

### **API Error Codes - English (Technical)**:
```typescript
const API_ERROR_CODES = {
  'VALIDATION_ERROR': 'Validation failed',
  'DUPLICATE_ENTRY': 'Duplicate entry found',
  'NOT_FOUND': 'Resource not found',
  'UNAUTHORIZED': 'Unauthorized access',
  'FORBIDDEN': 'Access forbidden',
  'INTERNAL_ERROR': 'Internal server error'
};
```

## 📊 **7. DATABASE SCHEMA - BILINGUAL DESIGN**

### **Core Tables - English Names (Technical)**:
```sql
-- Table names (English for consistency)
CREATE TABLE doctors (
  doctor_id VARCHAR(10) PRIMARY KEY,
  email VARCHAR(255) UNIQUE,
  phone_number VARCHAR(10),
  license_number VARCHAR(15) UNIQUE,
  specialty VARCHAR(50),        -- English value
  status VARCHAR(20),           -- English value
  created_at TIMESTAMP,
  updated_at TIMESTAMP
);

CREATE TABLE patients (
  patient_id VARCHAR(10) PRIMARY KEY,
  full_name VARCHAR(100),       -- Vietnamese name
  email VARCHAR(255) UNIQUE,
  phone_number VARCHAR(10),
  gender VARCHAR(10),           -- English value
  blood_type VARCHAR(5),        -- International standard
  status VARCHAR(20),           -- English value
  created_at TIMESTAMP,
  updated_at TIMESTAMP
);
```

### **Display Names Table - Vietnamese (UI)**:
```sql
CREATE TABLE field_translations (
  field_key VARCHAR(100) PRIMARY KEY,
  vietnamese_label VARCHAR(200),
  english_label VARCHAR(200),
  description TEXT
);

-- Sample data
INSERT INTO field_translations VALUES
('doctor_id', 'Mã bác sĩ', 'Doctor ID', 'Unique identifier for doctor'),
('full_name', 'Họ và tên', 'Full Name', 'Complete name of person'),
('specialty', 'Chuyên khoa', 'Specialty', 'Medical specialty'),
('phone_number', 'Số điện thoại', 'Phone Number', 'Contact phone number');
```

## 🌐 **8. API DESIGN - BILINGUAL**

### **Request/Response - English (Technical)**:
```json
// API Request (English)
POST /api/doctors
{
  "email": "<EMAIL>",
  "phone_number": "**********",
  "license_number": "VN-BS-1234",
  "specialty": "cardiology",
  "status": "active"
}

// API Response (English)
{
  "success": true,
  "data": {
    "doctor_id": "DOC000001",
    "specialty": "cardiology",
    "status": "active"
  },
  "message": "Doctor created successfully"
}
```

### **Frontend Display - Vietnamese (UI)**:
```typescript
// Frontend display transformation
const displayDoctor = {
  "ma_bac_si": "DOC000001",
  "chuyen_khoa": "Tim mạch",
  "trang_thai": "Đang làm việc"
};
```

## 🔄 **9. TRANSLATION SYSTEM**

### **Translation Helper Functions**:
```typescript
// Translation service
class TranslationService {
  
  // Translate specialty from English to Vietnamese
  translateSpecialty(englishSpecialty: string): string {
    return SPECIALTY_DISPLAY[englishSpecialty] || englishSpecialty;
  }
  
  // Translate status from English to Vietnamese
  translateStatus(englishStatus: string, type: string): string {
    return STATUS_DISPLAY[englishStatus] || englishStatus;
  }
  
  // Get field label in Vietnamese
  getFieldLabel(fieldKey: string): string {
    return FIELD_LABELS[fieldKey] || fieldKey;
  }
  
  // Format display data for Vietnamese UI
  formatForDisplay(data: any): any {
    return {
      ...data,
      specialty_display: this.translateSpecialty(data.specialty),
      status_display: this.translateStatus(data.status, 'doctor'),
      created_at_display: this.formatVietnameseDate(data.created_at)
    };
  }
}
```

### **Date/Time - Vietnamese Format**:
```typescript
// Date formatting for Vietnamese display
const formatVietnameseDate = (isoDate: string): string => {
  const date = new Date(isoDate);
  return date.toLocaleDateString('vi-VN', {
    day: '2-digit',
    month: '2-digit', 
    year: 'numeric'
  }); // Returns: 30/01/2025
};

const formatVietnameseDateTime = (isoDate: string): string => {
  const date = new Date(isoDate);
  return date.toLocaleString('vi-VN', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  }); // Returns: 30/01/2025, 14:30
};
```

## 📱 **10. FRONTEND IMPLEMENTATION**

### **Form Labels - Vietnamese**:
```typescript
const FORM_LABELS = {
  'doctor_id': 'Mã bác sĩ',
  'full_name': 'Họ và tên',
  'email': 'Email',
  'phone_number': 'Số điện thoại',
  'license_number': 'Số bằng cấp',
  'specialty': 'Chuyên khoa',
  'experience_years': 'Số năm kinh nghiệm',
  'consultation_fee': 'Phí khám bệnh',
  'department_id': 'Khoa',
  'status': 'Trạng thái',
  'working_hours': 'Lịch làm việc',
  'gender': 'Giới tính'
};
```

### **Dropdown Options - Vietnamese Display**:
```typescript
const SPECIALTY_OPTIONS = [
  { value: 'cardiology', label: 'Tim mạch' },
  { value: 'neurology', label: 'Thần kinh' },
  { value: 'pediatrics', label: 'Nhi khoa' },
  { value: 'surgery', label: 'Ngoại khoa' },
  { value: 'internal_medicine', label: 'Nội khoa' }
];

const STATUS_OPTIONS = [
  { value: 'active', label: 'Đang làm việc' },
  { value: 'inactive', label: 'Tạm nghỉ' },
  { value: 'on_leave', label: 'Nghỉ phép' },
  { value: 'retired', label: 'Nghỉ hưu' }
];
```

## 🎯 **11. IMPLEMENTATION STRATEGY**

### **Phase 1: Core Bilingual Structure**
1. ✅ Keep English for technical fields (IDs, status, specialty)
2. ✅ Use Vietnamese for user-facing labels and messages
3. ✅ Create translation mapping system
4. ✅ Implement bilingual validation

### **Phase 2: Enhanced Translation**
1. 🔧 Add translation service
2. 🔧 Implement dynamic label loading
3. 🔧 Create bilingual error handling
4. 🔧 Add date/time localization

### **Phase 3: Advanced Features**
1. 📋 Multi-language support (EN/VN toggle)
2. 📋 Context-aware translations
3. 📋 Medical terminology database
4. 📋 Localized reporting

## 🏆 **BENEFITS OF BILINGUAL APPROACH**

### **Technical Benefits**:
- ✅ **API Consistency** - English values for integration
- ✅ **Database Efficiency** - Standardized enum values
- ✅ **International Compatibility** - Medical terms in English
- ✅ **Code Maintainability** - Consistent technical naming

### **User Experience Benefits**:
- ✅ **Local Familiarity** - Vietnamese interface
- ✅ **Medical Accuracy** - Proper Vietnamese medical terms
- ✅ **Cultural Appropriateness** - Vietnam-specific formats
- ✅ **Accessibility** - Native language support

### **Business Benefits**:
- ✅ **Market Readiness** - Vietnam-optimized UX
- ✅ **Scalability** - Easy to add more languages
- ✅ **Integration Ready** - Standard technical formats
- ✅ **Compliance** - Local language requirements

---

**Document Version**: 3.0 (Bilingual Vietnam)  
**Last Updated**: 2025-01-30  
**Target Market**: Vietnam Healthcare (Bilingual)  
**Approach**: English (Technical) + Vietnamese (User Interface)
