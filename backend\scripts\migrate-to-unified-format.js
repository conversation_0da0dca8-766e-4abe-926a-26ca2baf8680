const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

function log(color, message) {
  const colors = {
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m',
    reset: '\x1b[0m'
  };
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// Generate new ID with unified format: {PREFIX}{YYYYMMDD}{SEQUENCE}
function generateUnifiedId(prefix, date = new Date(), sequence = 1) {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const seq = String(sequence).padStart(3, '0');
  
  return `${prefix}${year}${month}${day}${seq}`;
}

// Extract creation date from existing timestamp-based IDs
function extractDateFromTimestamp(timestampId) {
  const timestamp = timestampId.replace(/^[A-Z]+/, '');
  if (timestamp.length >= 10) {
    const epochSeconds = parseInt(timestamp.substring(0, 10));
    return new Date(epochSeconds * 1000);
  }
  return new Date(); // Fallback to current date
}

async function createBackup() {
  log('cyan', '📦 Creating comprehensive backup...');
  
  try {
    const tables = ['doctors', 'patients', 'appointments', 'departments', 'rooms', 'medical_records'];
    const backup = {
      timestamp: new Date().toISOString(),
      tables: {}
    };
    
    for (const table of tables) {
      const { data, error } = await supabase.from(table).select('*');
      if (error) {
        log('yellow', `⚠️  Could not backup ${table}: ${error.message}`);
        backup.tables[table] = [];
      } else {
        backup.tables[table] = data || [];
        log('blue', `   ✅ ${table}: ${data?.length || 0} records`);
      }
    }
    
    const fs = require('fs');
    const backupFile = `unified-format-backup-${Date.now()}.json`;
    fs.writeFileSync(backupFile, JSON.stringify(backup, null, 2));
    
    log('green', `✅ Backup created: ${backupFile}`);
    return backupFile;
  } catch (error) {
    log('red', `❌ Backup failed: ${error.message}`);
    throw error;
  }
}

async function migrateDoctors() {
  log('cyan', '\n🔧 Migrating Doctors to unified format...');
  
  try {
    const { data: doctors } = await supabase.from('doctors').select('*');
    const idMapping = {};
    
    for (let i = 0; i < doctors.length; i++) {
      const doctor = doctors[i];
      const oldId = doctor.doctor_id;
      
      // Extract date from existing ID or use creation date
      let creationDate = new Date(doctor.created_at);
      if (oldId.match(/^\w+\d{10}/)) {
        creationDate = extractDateFromTimestamp(oldId);
      }
      
      const newId = generateUnifiedId('DOC', creationDate, i + 1);
      idMapping[oldId] = newId;
      
      log('yellow', `   ${oldId} → ${newId}`);
      
      const { error } = await supabase
        .from('doctors')
        .update({ doctor_id: newId })
        .eq('doctor_id', oldId);
        
      if (error) {
        log('red', `❌ Failed to update ${oldId}: ${error.message}`);
      }
    }
    
    log('green', `✅ Migrated ${doctors.length} doctors`);
    return idMapping;
  } catch (error) {
    log('red', `❌ Doctor migration failed: ${error.message}`);
    throw error;
  }
}

async function migratePatients() {
  log('cyan', '\n🔧 Migrating Patients to unified format...');
  
  try {
    const { data: patients } = await supabase.from('patients').select('*');
    const idMapping = {};
    
    for (let i = 0; i < patients.length; i++) {
      const patient = patients[i];
      const oldId = patient.patient_id;
      
      let creationDate = new Date(patient.created_at);
      if (oldId.match(/^\w+\d{10}/)) {
        creationDate = extractDateFromTimestamp(oldId);
      }
      
      const newId = generateUnifiedId('PAT', creationDate, i + 1);
      idMapping[oldId] = newId;
      
      log('yellow', `   ${oldId} → ${newId}`);
      
      const { error } = await supabase
        .from('patients')
        .update({ patient_id: newId })
        .eq('patient_id', oldId);
        
      if (error) {
        log('red', `❌ Failed to update ${oldId}: ${error.message}`);
      }
    }
    
    log('green', `✅ Migrated ${patients.length} patients`);
    return idMapping;
  } catch (error) {
    log('red', `❌ Patient migration failed: ${error.message}`);
    throw error;
  }
}

async function migrateAppointments(doctorMapping, patientMapping) {
  log('cyan', '\n🔧 Migrating Appointments to unified format...');
  
  try {
    const { data: appointments } = await supabase.from('appointments').select('*');
    
    for (let i = 0; i < appointments.length; i++) {
      const appointment = appointments[i];
      const oldId = appointment.appointment_id;
      
      let creationDate = new Date(appointment.created_at);
      if (oldId.match(/^\w+\d{10}/)) {
        creationDate = extractDateFromTimestamp(oldId);
      }
      
      const newId = generateUnifiedId('APT', creationDate, i + 1);
      
      // Update appointment ID and foreign key references
      const updates = { appointment_id: newId };
      
      if (doctorMapping[appointment.doctor_id]) {
        updates.doctor_id = doctorMapping[appointment.doctor_id];
      }
      
      if (patientMapping[appointment.patient_id]) {
        updates.patient_id = patientMapping[appointment.patient_id];
      }
      
      log('yellow', `   ${oldId} → ${newId}`);
      if (updates.doctor_id !== appointment.doctor_id) {
        log('blue', `     doctor_id: ${appointment.doctor_id} → ${updates.doctor_id}`);
      }
      if (updates.patient_id !== appointment.patient_id) {
        log('blue', `     patient_id: ${appointment.patient_id} → ${updates.patient_id}`);
      }
      
      const { error } = await supabase
        .from('appointments')
        .update(updates)
        .eq('appointment_id', oldId);
        
      if (error) {
        log('red', `❌ Failed to update ${oldId}: ${error.message}`);
      }
    }
    
    log('green', `✅ Migrated ${appointments.length} appointments`);
  } catch (error) {
    log('red', `❌ Appointment migration failed: ${error.message}`);
    throw error;
  }
}

async function migrateDepartments() {
  log('cyan', '\n🔧 Migrating Departments to unified format...');
  
  try {
    const { data: departments } = await supabase.from('departments').select('*');
    const idMapping = {};
    
    for (let i = 0; i < departments.length; i++) {
      const department = departments[i];
      const oldId = department.department_id;
      
      let creationDate = new Date(department.created_at);
      if (oldId.match(/^\w+\d{10}/)) {
        creationDate = extractDateFromTimestamp(oldId);
      }
      
      const newId = generateUnifiedId('DEPT', creationDate, i + 1);
      idMapping[oldId] = newId;
      
      log('yellow', `   ${oldId} → ${newId}`);
      
      const { error } = await supabase
        .from('departments')
        .update({ department_id: newId })
        .eq('department_id', oldId);
        
      if (error) {
        log('red', `❌ Failed to update ${oldId}: ${error.message}`);
      }
    }
    
    log('green', `✅ Migrated ${departments.length} departments`);
    return idMapping;
  } catch (error) {
    log('red', `❌ Department migration failed: ${error.message}`);
    throw error;
  }
}

async function migrateRooms(departmentMapping) {
  log('cyan', '\n🔧 Migrating Rooms to unified format...');
  
  try {
    const { data: rooms } = await supabase.from('rooms').select('*');
    
    for (let i = 0; i < rooms.length; i++) {
      const room = rooms[i];
      const oldId = room.room_id;
      
      let creationDate = new Date(room.created_at);
      if (oldId.match(/^\w+\d{10}/)) {
        creationDate = extractDateFromTimestamp(oldId);
      }
      
      const newId = generateUnifiedId('ROOM', creationDate, i + 1);
      
      const updates = { room_id: newId };
      
      if (departmentMapping[room.department_id]) {
        updates.department_id = departmentMapping[room.department_id];
      }
      
      log('yellow', `   ${oldId} → ${newId}`);
      if (updates.department_id !== room.department_id) {
        log('blue', `     department_id: ${room.department_id} → ${updates.department_id}`);
      }
      
      const { error } = await supabase
        .from('rooms')
        .update(updates)
        .eq('room_id', oldId);
        
      if (error) {
        log('red', `❌ Failed to update ${oldId}: ${error.message}`);
      }
    }
    
    log('green', `✅ Migrated ${rooms.length} rooms`);
  } catch (error) {
    log('red', `❌ Room migration failed: ${error.message}`);
    throw error;
  }
}

async function migrateMedicalRecords(doctorMapping, patientMapping) {
  log('cyan', '\n🔧 Migrating Medical Records to unified format...');
  
  try {
    const { data: records } = await supabase.from('medical_records').select('*');
    
    for (let i = 0; i < records.length; i++) {
      const record = records[i];
      const oldId = record.record_id;
      
      let creationDate = new Date(record.created_at);
      if (oldId.match(/^\w+\d{10}/)) {
        creationDate = extractDateFromTimestamp(oldId);
      }
      
      const newId = generateUnifiedId('MED', creationDate, i + 1);
      
      const updates = { record_id: newId };
      
      if (doctorMapping[record.doctor_id]) {
        updates.doctor_id = doctorMapping[record.doctor_id];
      }
      
      if (patientMapping[record.patient_id]) {
        updates.patient_id = patientMapping[record.patient_id];
      }
      
      log('yellow', `   ${oldId} → ${newId}`);
      
      const { error } = await supabase
        .from('medical_records')
        .update(updates)
        .eq('record_id', oldId);
        
      if (error) {
        log('red', `❌ Failed to update ${oldId}: ${error.message}`);
      }
    }
    
    log('green', `✅ Migrated ${records.length} medical records`);
  } catch (error) {
    log('red', `❌ Medical record migration failed: ${error.message}`);
    throw error;
  }
}

async function verifyMigration() {
  log('cyan', '\n🔍 Verifying migration results...');
  
  const checks = [
    { table: 'doctors', pattern: /^DOC\d{8}\d{3}$/, prefix: 'DOC' },
    { table: 'patients', pattern: /^PAT\d{8}\d{3}$/, prefix: 'PAT' },
    { table: 'appointments', pattern: /^APT\d{8}\d{3}$/, prefix: 'APT' },
    { table: 'departments', pattern: /^DEPT\d{8}\d{3}$/, prefix: 'DEPT' },
    { table: 'rooms', pattern: /^ROOM\d{8}\d{3}$/, prefix: 'ROOM' },
    { table: 'medical_records', pattern: /^MED\d{8}\d{3}$/, prefix: 'MED' }
  ];
  
  let allPassed = true;
  
  for (const check of checks) {
    try {
      const { data } = await supabase.from(check.table).select('*');
      const idField = check.table === 'medical_records' ? 'record_id' : 
                     check.table === 'departments' ? 'department_id' :
                     check.table === 'doctors' ? 'doctor_id' :
                     check.table === 'patients' ? 'patient_id' :
                     check.table === 'appointments' ? 'appointment_id' :
                     'room_id';
      
      const invalidIds = data?.filter(record => !check.pattern.test(record[idField])) || [];
      
      if (invalidIds.length === 0) {
        log('green', `   ✅ ${check.table}: All ${data?.length || 0} records have valid format`);
      } else {
        log('red', `   ❌ ${check.table}: ${invalidIds.length} records have invalid format`);
        allPassed = false;
      }
    } catch (error) {
      log('red', `   ❌ ${check.table}: Verification failed - ${error.message}`);
      allPassed = false;
    }
  }
  
  return allPassed;
}

async function main() {
  log('cyan', '🚀 Hospital Management System - Unified Format Migration');
  log('cyan', '=========================================================');
  
  try {
    // Step 1: Create backup
    const backupFile = await createBackup();
    
    // Step 2: Migrate core entities first
    const doctorMapping = await migrateDoctors();
    const patientMapping = await migratePatients();
    const departmentMapping = await migrateDepartments();
    
    // Step 3: Migrate entities with foreign keys
    await migrateAppointments(doctorMapping, patientMapping);
    await migrateRooms(departmentMapping);
    await migrateMedicalRecords(doctorMapping, patientMapping);
    
    // Step 4: Verify migration
    const success = await verifyMigration();
    
    if (success) {
      log('green', '\n🎉 Migration completed successfully!');
      log('blue', `📦 Backup saved as: ${backupFile}`);
      log('cyan', '\n✅ All tables now use unified format standards:');
      log('yellow', '   - DOC{YYYYMMDD}{XXX} for doctors');
      log('yellow', '   - PAT{YYYYMMDD}{XXX} for patients');
      log('yellow', '   - APT{YYYYMMDD}{XXX} for appointments');
      log('yellow', '   - DEPT{YYYYMMDD}{XXX} for departments');
      log('yellow', '   - ROOM{YYYYMMDD}{XXX} for rooms');
      log('yellow', '   - MED{YYYYMMDD}{XXX} for medical records');
    } else {
      log('red', '\n❌ Migration completed with issues. Please review and fix manually.');
    }
    
  } catch (error) {
    log('red', `❌ Migration failed: ${error.message}`);
    log('yellow', 'Please restore from backup if needed.');
    process.exit(1);
  }
}

// Confirmation prompt
const readline = require('readline');
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

log('yellow', '⚠️  This script will migrate ALL tables to unified format standards.');
log('yellow', '   This is a MAJOR change that affects all primary keys and foreign keys.');
log('yellow', '   A comprehensive backup will be created before making changes.');
log('cyan', '\n📋 Migration will affect:');
log('blue', '   - doctors: DOC001 → DOC20250118001');
log('blue', '   - patients: PAT1747555777 → PAT20250118001');
log('blue', '   - appointments: APT1747555777 → APT20250118001');
log('blue', '   - departments: DEPT1747555777 → DEPT20250118001');
log('blue', '   - rooms: ROOM1747555777 → ROOM20250118001');
log('blue', '   - medical_records: MR1747555777 → MED20250118001');

rl.question('\nDo you want to proceed with unified format migration? (yes/no): ', (answer) => {
  if (answer.toLowerCase() === 'yes' || answer.toLowerCase() === 'y') {
    rl.close();
    main();
  } else {
    log('blue', 'Migration cancelled.');
    rl.close();
  }
});
