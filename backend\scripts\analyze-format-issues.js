const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

function log(color, message) {
  const colors = {
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m',
    reset: '\x1b[0m'
  };
  console.log(`${colors[color]}${message}${colors.reset}`);
}

async function analyzeFormatIssues() {
  log('cyan', '🔍 Detailed Analysis of Format Issues');
  log('cyan', '=====================================');

  // Check doctors table issues
  log('blue', '\n📋 DOCTORS TABLE - Format Issues:');
  const { data: doctors } = await supabase.from('doctors').select('*');
  
  log('yellow', '\n🔴 Doctor ID Format Issues:');
  doctors.forEach(doctor => {
    if (!/^DOC\d{6}$/.test(doctor.doctor_id)) {
      log('red', `   ${doctor.doctor_id} - Should be DOC + 6 digits (e.g., DOC000001)`);
      log('yellow', `     Current: "${doctor.doctor_id}"`);
      log('yellow', `     Expected: DOC${String(doctors.indexOf(doctor) + 1).padStart(6, '0')}`);
    }
  });

  log('yellow', '\n🔴 License Number Format Issues:');
  doctors.forEach(doctor => {
    if (!/^[A-Z]{2,4}\d{6,10}$/.test(doctor.license_number)) {
      log('red', `   ${doctor.doctor_id}: "${doctor.license_number}"`);
      log('yellow', `     Should be 2-4 letters + 6-10 digits (e.g., BS001235, MD123456)`);
    }
  });

  // Check appointments table issues
  log('blue', '\n📋 APPOINTMENTS TABLE - Format Issues:');
  const { data: appointments } = await supabase.from('appointments').select('*');
  
  log('yellow', '\n🔴 Doctor ID References:');
  appointments.forEach(apt => {
    if (!/^DOC\d{6}$/.test(apt.doctor_id)) {
      log('red', `   ${apt.appointment_id}: doctor_id "${apt.doctor_id}"`);
      log('yellow', `     References invalid doctor ID format`);
    }
  });

  // Check medical records table issues
  log('blue', '\n📋 MEDICAL_RECORDS TABLE - Format Issues:');
  const { data: medicalRecords } = await supabase.from('medical_records').select('*');
  
  log('yellow', '\n🔴 Doctor ID References:');
  medicalRecords.forEach(record => {
    if (!/^DOC\d{6}$/.test(record.doctor_id)) {
      log('red', `   ${record.record_id}: doctor_id "${record.doctor_id}"`);
      log('yellow', `     References invalid doctor ID format`);
    }
  });

  // Show current data patterns
  log('cyan', '\n📊 CURRENT DATA PATTERNS:');
  
  log('blue', '\n🔍 Doctor IDs in database:');
  const doctorIds = doctors.map(d => d.doctor_id).sort();
  doctorIds.forEach(id => {
    const isValid = /^DOC\d{6}$/.test(id);
    log(isValid ? 'green' : 'red', `   ${id} ${isValid ? '✅' : '❌'}`);
  });

  log('blue', '\n🔍 License Numbers in database:');
  const licenseNumbers = doctors.map(d => ({ id: d.doctor_id, license: d.license_number }));
  licenseNumbers.forEach(({ id, license }) => {
    const isValid = /^[A-Z]{2,4}\d{6,10}$/.test(license);
    log(isValid ? 'green' : 'red', `   ${id}: "${license}" ${isValid ? '✅' : '❌'}`);
  });

  // Generate migration suggestions
  log('cyan', '\n🔧 MIGRATION SUGGESTIONS:');
  
  log('yellow', '\n1. Doctor ID Standardization:');
  doctors.forEach((doctor, index) => {
    if (!/^DOC\d{6}$/.test(doctor.doctor_id)) {
      const newId = `DOC${String(index + 1).padStart(6, '0')}`;
      log('blue', `   UPDATE doctors SET doctor_id = '${newId}' WHERE doctor_id = '${doctor.doctor_id}';`);
    }
  });

  log('yellow', '\n2. License Number Standardization:');
  doctors.forEach(doctor => {
    if (!/^[A-Z]{2,4}\d{6,10}$/.test(doctor.license_number)) {
      // Extract numbers and letters
      const letters = doctor.license_number.match(/[A-Z]+/g)?.join('') || 'MD';
      const numbers = doctor.license_number.match(/\d+/g)?.join('') || '000000';
      const standardized = letters.substring(0, 4) + numbers.padStart(6, '0').substring(0, 10);
      log('blue', `   UPDATE doctors SET license_number = '${standardized}' WHERE doctor_id = '${doctor.doctor_id}';`);
    }
  });

  log('yellow', '\n3. Foreign Key Updates (after doctor ID changes):');
  appointments.forEach(apt => {
    if (!/^DOC\d{6}$/.test(apt.doctor_id)) {
      // Find corresponding doctor
      const doctorIndex = doctors.findIndex(d => d.doctor_id === apt.doctor_id);
      if (doctorIndex !== -1) {
        const newDoctorId = `DOC${String(doctorIndex + 1).padStart(6, '0')}`;
        log('blue', `   UPDATE appointments SET doctor_id = '${newDoctorId}' WHERE appointment_id = '${apt.appointment_id}';`);
      }
    }
  });

  medicalRecords.forEach(record => {
    if (!/^DOC\d{6}$/.test(record.doctor_id)) {
      const doctorIndex = doctors.findIndex(d => d.doctor_id === record.doctor_id);
      if (doctorIndex !== -1) {
        const newDoctorId = `DOC${String(doctorIndex + 1).padStart(6, '0')}`;
        log('blue', `   UPDATE medical_records SET doctor_id = '${newDoctorId}' WHERE record_id = '${record.record_id}';`);
      }
    }
  });

  // Summary statistics
  log('cyan', '\n📈 STATISTICS:');
  const totalDoctors = doctors.length;
  const invalidDoctorIds = doctors.filter(d => !/^DOC\d{6}$/.test(d.doctor_id)).length;
  const invalidLicenses = doctors.filter(d => !/^[A-Z]{2,4}\d{6,10}$/.test(d.license_number)).length;
  const affectedAppointments = appointments.filter(a => !/^DOC\d{6}$/.test(a.doctor_id)).length;
  const affectedMedicalRecords = medicalRecords.filter(m => !/^DOC\d{6}$/.test(m.doctor_id)).length;

  log('blue', `📊 Total doctors: ${totalDoctors}`);
  log('red', `❌ Invalid doctor IDs: ${invalidDoctorIds} (${(invalidDoctorIds/totalDoctors*100).toFixed(1)}%)`);
  log('red', `❌ Invalid license numbers: ${invalidLicenses} (${(invalidLicenses/totalDoctors*100).toFixed(1)}%)`);
  log('red', `❌ Affected appointments: ${affectedAppointments}`);
  log('red', `❌ Affected medical records: ${affectedMedicalRecords}`);

  log('cyan', '\n🎯 PRIORITY ACTIONS:');
  log('yellow', '1. 🔴 HIGH: Fix doctor_id format (affects foreign keys)');
  log('yellow', '2. 🟡 MEDIUM: Standardize license_number format');
  log('yellow', '3. 🟢 LOW: Update validation rules to prevent future issues');
  
  log('cyan', '\n💡 IMPLEMENTATION PLAN:');
  log('blue', '1. Create backup of current data');
  log('blue', '2. Run doctor ID migration script');
  log('blue', '3. Update foreign key references');
  log('blue', '4. Standardize license numbers');
  log('blue', '5. Test data integrity');
  log('blue', '6. Deploy updated validation rules');
}

analyzeFormatIssues().catch(console.error);
