{"timestamp": "2025-05-30T16:59:41.363Z", "migration_completed": true, "format_standards": {"primary_keys": "{PREFIX}{YYYYMMDD}{XXX}", "license_numbers": "VN-{TYPE}-{NUMBER}", "date_format": "ISO 8601", "phone_format": "E.164 International"}, "compliance_summary": {"doctors": {"total_records": 11, "compliant_records": 11, "compliance_rate": "100%"}, "patients": {"total_records": 10, "compliant_records": 10, "compliance_rate": "100%"}, "appointments": {"total_records": 5, "compliant_records": 5, "compliance_rate": "100%"}, "departments": {"total_records": 8, "compliant_records": 8, "compliance_rate": "100%"}, "rooms": {"total_records": 2, "compliant_records": 2, "compliance_rate": "100%"}, "medical_records": {"total_records": 2, "compliant_records": 2, "compliance_rate": "100%"}}, "recommendations": ["Implement validation rules in all services", "Add database constraints for format enforcement", "Setup monitoring for format compliance", "Update API documentation with new formats", "Train team on new format standards"]}